<template>
  <div class="algorithm-edit-container">
    <div class="header-container">
      <el-page-header
        ref="pageHeader"
        @back="goBack"
      >
        <template #content>
          <span class="algorithm-name">{{ algorithmName }}</span>
          <span class="algorithm-title text-primary">算法编辑</span>
        </template>
      </el-page-header>
    </div>

    <div class="content-container">
      <div class="card-header">
        <el-input
          v-model="algorithmName"
          placeholder="算法名称"
          class="algorithm-name-input"
          maxlength="32"
          show-word-limit
        />
        <el-button
          type="primary"
          @click="saveAlgorithm"
        >
          保存
        </el-button>
      </div>

      <div class="editor-container">
        <div
          ref="codeEditor"
          class="code-editor"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios' // 确保引入axios
import { getArithmeticDetail, updateArithmetic, createArithmetic } from '@/api'

// 不要使用import导入monaco，而是在运行时动态加载
// import * as monaco from 'monaco-editor'
let monaco = null

const route = useRoute()
const router = useRouter()
const algorithmId = route.params.id
// 保留algorithmType以便将来使用，虽然现在未使用
const algorithmName = ref(route.query.name || '新建算法')

// 编辑器实例
let editor = null
const codeEditor = ref(null)

// 初始代码内容，会在加载完成后更新
const initialCode = ref('')
const loading = ref(false)

// 获取算法详情
const loadAlgorithmContent = async () => {
  if (!algorithmId || algorithmId === 'new') {
    // 新建算法，加载示例代码
    await loadExampleCode()
    return
  }

  try {
    loading.value = true
    const response = await getArithmeticDetail(algorithmId)

    if (response.success) {
      // 获取算法内容
      if (response.data.content) {
        initialCode.value = response.data.content
      } else {
        await loadExampleCode() // 如果没有内容，加载示例代码
      }

      // 更新算法名称
      algorithmName.value = response.data.name
    } else {
      ElMessage.error('获取算法内容失败')
      await loadExampleCode() // 加载失败，使用示例代码
    }
  } catch (error) {
    console.error('获取算法内容失败:', error)
    ElMessage.error('获取算法内容失败: ' + (error.message || '未知错误'))
    await loadExampleCode() // 加载失败，使用示例代码
  } finally {
    loading.value = false
  }
}

// 使用axios获取示例文件内容
const loadExampleCode = async () => {
  try {
    // 获取当前算法的类型（如果能从路由查询参数中获取）
    const algorithmType = Number(route.query.type)
    let exampleFile = '/example_clean.js' // 默认使用清洗算法示例

    // 根据算法类型选择不同的示例文件
    if (algorithmType === 1) {
      // 分析算法
      exampleFile = '/example_process.js'
    } else if (algorithmType === 0) {
      // 清洗算法
      exampleFile = '/example_clean.js'
    }

    // 使用axios请求示例代码
    const response = await axios.get(exampleFile, {
      // 防止axios自动解析JSON
      transformResponse: [data => data]
    })

    initialCode.value = response.data
  } catch (error) {
    console.error('加载示例代码失败:', error)
    ElMessage.error('加载示例代码失败，将使用空白内容')
    initialCode.value = '// 在此处编写您的算法代码'
  }
}

// 页面头部引用
const pageHeader = ref(null)

// 动态加载Monaco编辑器
const loadMonacoEditor = () => {
  return new Promise((resolve, reject) => {
    // 如果已经加载过，直接返回
    if (window.monaco) {
      monaco = window.monaco
      resolve(monaco)
      return
    }

    // 设置Monaco环境配置 - 使用Blob URL解决跨域问题
    window.MonacoEnvironment = {
      getWorker: function (_workerId, _label) {
        const workerSourceURL =
          'https://mhp-tech.oss-cn-hangzhou.aliyuncs.com/npm/monaco-editor/0.52.2/min/vs/base/worker/workerMain.js'

        // 创建一个Blob URL，内容是在主线程中加载Worker代码
        const blob = new Blob(
          [
            `self.MonacoEnvironment = { baseUrl: 'https://mhp-tech.oss-cn-hangzhou.aliyuncs.com/npm/monaco-editor/0.52.2/min/' };
           importScripts('${workerSourceURL}');`
          ],
          { type: 'application/javascript' }
        )

        return new Worker(URL.createObjectURL(blob))
      }
    }

    // 动态加载Monaco编辑器脚本
    const script = document.createElement('script')
    script.src =
      'https://mhp-tech.oss-cn-hangzhou.aliyuncs.com/npm/monaco-editor/0.52.2/min/vs/loader.js'
    script.onload = () => {
      window.require.config({
        paths: {
          vs: 'https://mhp-tech.oss-cn-hangzhou.aliyuncs.com/npm/monaco-editor/0.52.2/min/vs'
        }
      })

      window.require(['vs/editor/editor.main'], () => {
        monaco = window.monaco
        resolve(monaco)
      })
    }
    script.onerror = reject
    document.body.appendChild(script)
  })
}

// 初始化编辑器
const initEditor = async () => {
  if (editor) {
    editor.dispose()
  }

  try {
    // 确保Monaco已加载
    if (!monaco) {
      monaco = await loadMonacoEditor()
    }

    // 确保示例代码已加载
    if (!initialCode.value) {
      console.warn('编辑器初始化时代码内容为空')
    }

    editor = monaco.editor.create(codeEditor.value, {
      value: initialCode.value, // 使用已加载的代码
      language: 'javascript',
      theme: 'vs',
      automaticLayout: true,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      lineNumbers: 'on',
      folding: true,
      fontSize: 14,
      tabSize: 2
    })
  } catch (error) {
    console.error('初始化编辑器失败:', error)
    ElMessage.error('编辑器初始化失败，请刷新页面重试')
  }
}

// 保存算法
const saveAlgorithm = async () => {
  if (!editor) {
    ElMessage.error('编辑器未正确初始化')
    return
  }

  // 获取编辑器中的语法错误标记
  const model = editor.getModel()
  const markers = monaco.editor.getModelMarkers({ resource: model.uri })
  const errors = markers.filter(marker => marker.severity === monaco.MarkerSeverity.Error)

  if (errors.length > 0) {
    // 有语法错误，显示错误信息并阻止保存
    const firstError = errors[0]
    ElMessage.error(`代码第${firstError.startLineNumber}行存在语法错误: ${firstError.message}`)
    // 可选：聚焦到错误位置
    editor.revealPositionInCenter({
      lineNumber: firstError.startLineNumber,
      column: firstError.startColumn
    })
    return
  }

  const code = editor.getValue()

  try {
    loading.value = true

    // 准备更新数据
    const updateData = {
      name: algorithmName.value,
      content: code
    }

    // let currentAlgorithmId = algorithmId

    if (algorithmId && algorithmId !== 'new') {
      // 更新现有算法
      const response = await updateArithmetic(algorithmId, updateData)
      if (!response.success) {
        ElMessage.error('算法保存失败')
        return
      }
    } else {
      // 创建新算法
      const response = await createArithmetic(updateData)
      if (!response.success) {
        ElMessage.error('算法创建失败')
        return
      }
      // currentAlgorithmId = response.data.id
    }

    ElMessage.success('算法保存成功')
    
    // 获取算法暴露的方法列表
    // const methodsResponse = await getAlgorithmMethods(currentAlgorithmId)
    // if (methodsResponse.success) {
    //   const methods = methodsResponse.data
    //   if (methods.length > 0) {
    //     ElMessage.success(`算法已加载,可用方法: ${methods.join(', ')}`)
    //   } else {
    //     ElMessage.warning('算法已加载,但未暴露任何方法')
    //   }
    // }
  } catch (error) {
    console.error('保存算法失败:', error)
    ElMessage.error('保存算法失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(async () => {
  try {
    // 先加载算法内容
    await loadAlgorithmContent()

    // 延迟初始化编辑器，确保DOM已完全渲染
    setTimeout(() => {
      initEditor()
    }, 100)
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  }
})

// 组件卸载前清理编辑器
onBeforeUnmount(() => {
  if (editor) {
    editor.dispose()
    editor = null
  }
})
</script>

<style scoped>
/* 将整个页面设置为一个网格布局 */
.algorithm-edit-container {
  display: grid;
  grid-template-rows: auto 1fr;
  height: 100%;
  padding: 20px;
  background-color: #f5f7fa;
  box-sizing: border-box;
}

/* 头部区域 */
.header-container {
  margin-bottom: 15px;
}

/* 内容区域 - 使用网格布局 */
.content-container {
  display: grid;
  grid-template-rows: auto 1fr;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  height: 60px;
  border-bottom: 1px solid #ebeef5;
}

/* 编辑器容器 */
.editor-container {
  padding: 10px;
  overflow: hidden;
}

/* 代码编辑器 */
.code-editor {
  height: 100%;
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.algorithm-name {
  font-size: 18px;
  font-weight: bold;
  margin-right: 16px;
}

.algorithm-title {
  font-size: 14px;
  background-color: rgba(0, 113, 227, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
}

.algorithm-name-input {
  width: 300px;
}

/* 优化页面头部样式，减少额外空间 */
:deep(.el-page-header) {
  padding: 0;
  margin-bottom: 10px;
}

/* 优化卡片内部padding */
:deep(.el-card__body) {
  padding: 10px !important;
}
</style>

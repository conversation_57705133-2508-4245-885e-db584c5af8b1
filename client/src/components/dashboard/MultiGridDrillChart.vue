<template>
  <div class="multi-grid-drill-chart">
    <div
      ref="chartRef"
      class="chart-container"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

// 定义props
interface Props {
  drillingData?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  drillingData: () => []
})

// 图表相关
const chartRef = ref<HTMLDivElement>()
const chartInstance = ref<echarts.ECharts>()

// 参数配置 - 匹配原始组件的5个参数，使用统一绿色
const paramConfigs = [
  { name: '钻进速度 m/min', unit: 'm/min', field: 'advncSpd', color: '#389E0D' },
  { name: '旋转扭矩 Nm', unit: 'Nm', field: 'rtnTq', color: '#389E0D' },
  { name: '水压力 MPa', unit: 'MPa', field: 'wtrPrsH', color: '#389E0D' },
  { name: '旋转速度 rpm', unit: 'rpm', field: 'rtnSpd', color: '#389E0D' },
  { name: '推进力 kN', unit: 'kN', field: 'frcstKn', color: '#389E0D' }
]

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 销毁现有图表实例
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }

  // 创建新的图表实例
  chartInstance.value = echarts.init(chartRef.value)

  // 处理数据
  const data = props.drillingData || []
  const timeData: string[] = []
  const paramData: { [key: string]: [string, number][] } = {}

  // 初始化参数数据数组
  paramConfigs.forEach(config => {
    paramData[config.field] = []
  })

  // 处理每个数据点
  data.forEach(item => {
    // 使用采集时间作为X轴
    const timeValue = item.collectionAt || item.Collection_At || new Date().toISOString()
    timeData.push(timeValue)

    // 处理各个参数字段 - 兼容不同的字段名格式
    paramConfigs.forEach(config => {
      // 支持驼峰和下划线两种格式，优先使用实际MQTT数据中的字段名
      let value = 0
      if (config.field === 'advncSpd') {
        value = Number(item.Advnc_Spd || item.advncSpd) || 0
      } else if (config.field === 'rtnTq') {
        value = Number(item.Rtn_Tq || item.rtnTq) || 0
      } else if (config.field === 'wtrPrsH') {
        value = Number(item.Wtr_Prs_H || item.wtrPrsH) || 0
      } else if (config.field === 'rtnSpd') {
        value = Number(item.Rtn_Spd || item.rtnSpd) || 0
      } else if (config.field === 'frcstKn') {
        value = Number(item.Frcst_kN || item.frcstKn) || 0
      }
      paramData[config.field].push([timeValue, value])
    })
  })

  // 计算网格布局 - 匹配原始组件的配置
  const gridCount = paramConfigs.length
  const topMargin = 100  // 顶部边距，为标题和图例留出空间
  const bottomMargin = 70  // 底部边距，为数据缩放控制器留出空间
  const gridGap = 30  // 网格之间的间距
  const totalHeight = 750  // 默认总高度，匹配原始组件
  const availableHeight = totalHeight - topMargin - bottomMargin
  const gridHeight = Math.floor((availableHeight - (gridCount - 1) * gridGap) / gridCount)

  // 创建网格、轴和系列配置
  const grids: any[] = []
  const xAxes: any[] = []
  const yAxes: any[] = []
  const series: any[] = []

  // 为每个参数创建独立的网格
  paramConfigs.forEach((config, index) => {
    const gridTop = topMargin + index * (gridHeight + gridGap)

    // 网格配置 - 匹配原始组件
    grids.push({
      top: gridTop,
      height: gridHeight,
      left: 60,  // 留出Y轴标签的空间
      right: 20
    })

    // X轴配置 - 只在最底部显示标签，匹配原始组件样式
    xAxes.push({
      type: 'time',
      gridIndex: index,
      position: 'bottom',
      axisLabel: index === gridCount - 1 ? {
        color: '#333', // 深色标签，适合白色背景
        fontSize: 11,
        formatter: (value: any) => {
          const date = new Date(value)
          return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`
        }
      } : { show: false },
      axisLine: {
        show: index === gridCount - 1,
        lineStyle: { color: '#333' }
      },
      axisTick: {
        show: index === gridCount - 1,
        lineStyle: { color: '#333' }
      },
      splitLine: { show: false },
      boundaryGap: false,
      // 为每个X轴配置自定义的axisPointer标签 - 显示灰色区域和参数值
      axisPointer: {
        label: {
          backgroundColor: '#ccc',
          borderColor: '#aaa',
          borderWidth: 1,
          shadowBlur: 0,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          // 自定义灰色标签显示内容 - 参考DrillCurveChartCard.vue的实现
          formatter: (function(currentConfig, currentData, currentTimeData) {
            return function(params: any) {
              // 添加调试信息
              console.log('MultiGrid AxisPointer formatter called:', {
                params: params,
                currentConfig: currentConfig,
                currentDataLength: currentData ? currentData.length : 0,
                currentTimeDataLength: currentTimeData ? currentTimeData.length : 0
              });

              // 基础验证
              if (!currentConfig) {
                console.log('No config available');
                return '无配置数据';
              }

              if (!currentData || currentData.length === 0) {
                console.log('No current data available');
                return '无数据';
              }

              let dataIndex = -1;

              // 优先使用 params.seriesData 获取精确的数据索引
              if (params.seriesData && params.seriesData.length > 0) {
                console.log('Using seriesData strategy');
                // 从seriesData中获取第一个有效的dataIndex
                for (let i = 0; i < params.seriesData.length; i++) {
                  const seriesItem = params.seriesData[i];
                  if (seriesItem.dataIndex !== undefined && seriesItem.dataIndex >= 0) {
                    dataIndex = seriesItem.dataIndex;
                    console.log('Found dataIndex from seriesData:', dataIndex);
                    break;
                  }
                }
              }

              // 如果seriesData没有提供有效的dataIndex，使用降级策略
              if (dataIndex === -1) {
                // 尝试使用 params.dataIndex
                if (params.dataIndex !== undefined && params.dataIndex >= 0 && params.dataIndex < currentTimeData.length) {
                  dataIndex = params.dataIndex;
                  console.log('Using params.dataIndex:', dataIndex);
                } else {
                  // 最后的降级方案：通过时间值查找第一个匹配的索引
                  const currentTime = params.value;
                  console.log('Using time value matching for:', currentTime);
                  for (let i = 0; i < currentTimeData.length; i++) {
                    if (new Date(currentTimeData[i]).getTime() === new Date(currentTime).getTime()) {
                      dataIndex = i;
                      console.log('Found dataIndex by time matching:', dataIndex);
                      break;
                    }
                  }
                }
              }

              if (dataIndex === -1 || dataIndex >= currentTimeData.length) {
                console.log('Could not find valid dataIndex');
                return '找不到对应数据';
              }

              console.log('Final dataIndex:', dataIndex);

              // 显示当前参数的值 - 从时间序列数据中获取对应索引的值
              // currentData是时间序列格式 [time, value][]，需要获取对应时间点的值
              let value = null;
              if (currentData && currentData.length > dataIndex) {
                // 从时间序列数据中提取数值部分
                const dataPoint = currentData[dataIndex];
                if (Array.isArray(dataPoint) && dataPoint.length >= 2) {
                  value = dataPoint[1]; // 时间序列数据的第二个元素是数值
                  console.log('Extracted value from time series:', value);
                } else {
                  console.log('Invalid data point structure:', dataPoint);
                }
              } else {
                console.log('Data index out of range or no data');
              }

              const formattedValue = value !== null && value !== undefined ?
                (typeof value === 'number' ? value.toFixed(2) : value) :
                '无数据';

              const result = `${currentConfig.name}: ${formattedValue} ${currentConfig.unit}`;
              console.log('Final result:', result);
              return result;
            }
          })(config, paramData[config.field], timeData)
        }
      }
    })

    // Y轴配置 - 匹配原始组件样式（白色背景）
    yAxes.push({
      type: 'value',
      gridIndex: index,
      name: config.name,
      nameTextStyle: {
        color: config.color, // 使用参数颜色作为Y轴标题颜色
        fontSize: 12,
        fontWeight: 'bold',
        padding: [0, 0, 0, 0]
      },
      axisLabel: {
        color: config.color, // 使用参数颜色作为Y轴标签颜色
        fontSize: 11,
        formatter: '{value}'
      },
      axisLine: {
        show: true,
        lineStyle: { color: config.color } // 使用参数颜色作为Y轴线颜色
      },
      axisTick: {
        show: true,
        lineStyle: { color: config.color }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(233, 233, 233, 0.5)', // 匹配原组件的网格线颜色
          type: 'dashed'
        }
      }
    })

    // 系列配置 - 匹配原始组件样式，包含面积填充
    series.push({
      name: config.name,
      type: 'line',
      xAxisIndex: index,
      yAxisIndex: index,
      data: paramData[config.field],
      showSymbol: false,
      connectNulls: true,
      lineStyle: {
        color: config.color,
        width: 2
      },
      itemStyle: {
        color: config.color
      },
      // 添加面积填充，匹配原始组件
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: config.color + '40' // 40%透明度
          }, {
            offset: 1,
            color: config.color + '10' // 10%透明度
          }]
        }
      },
      smooth: false
    })
  })

  // 图表配置 - 匹配原始组件（白色背景）
  const option = {
    // 不设置backgroundColor，使用默认白色背景，匹配原组件
    animation: false,
    title: {
      text: '钻进深度曲线',
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#303133'
      }
    },
    legend: {
      data: paramConfigs.map(config => config.name),
      top: 40,
      left: 'center',
      textStyle: {
        fontSize: 12,
        color: '#606266'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        animation: false
      },
      triggerOn: 'mousemove|click',
      confine: true,
      enterable: true,
      extraCssText: 'max-width: 250px;',
      // 固定tooltip位置到右上角，匹配原组件
      position: ['86%', 0],
      formatter: (params: any) => {
        if (!params || params.length === 0) return ''
        // 只显示X轴数据（时间），匹配原组件的简化显示
        // 因为灰色区域已经显示了各自的参数数据
        const xValue = params[0]?.axisValue
        const date = new Date(xValue)
        const timeStr = date.getHours().toString().padStart(2, '0') + ':' +
                      date.getMinutes().toString().padStart(2, '0') + ':' +
                      date.getSeconds().toString().padStart(2, '0')
        return `采集时间: ${timeStr}`
      }
    },
    axisPointer: {
      link: {xAxisIndex: 'all'} // 链接所有X轴，匹配原组件
    },
    toolbox: {
      feature: {
        dataView: { readOnly: false },
        saveAsImage: {}
      }
    },
    // 添加数据缩放控制器，匹配原始组件
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: Array.from({length: gridCount}, (_, idx) => idx),
        start: 0,
        end: 100,
        filterMode: 'filter',
        handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6V24.4z M13.3,22H6.7v-1.2h6.6V22z M13.3,19.6H6.7v-1.2h6.6V19.6z',
        handleSize: '80%',
        handleStyle: {
          color: '#fff',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2
        }
      },
      {
        type: 'inside',
        xAxisIndex: Array.from({length: gridCount}, (_, idx) => idx),
        start: 0,
        end: 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true
      }
    ],
    grid: grids,
    xAxis: xAxes,
    yAxis: yAxes,
    series: series
  }

  // 设置图表配置
  chartInstance.value.setOption(option)
}

// 监听数据变化
watch(() => props.drillingData, () => {
  nextTick(() => {
    initChart()
  })
}, { deep: true })

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 组件卸载
onBeforeUnmount(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
})


</script>

<style scoped>
.multi-grid-drill-chart {
  width: 100%;
  height: 750px; /* 匹配原始组件的默认高度 */
  background: transparent; /* 使用透明背景，让父容器控制背景 */
  padding: 0; /* 移除内边距，让图表占满容器 */
}

.chart-container {
  width: 100%;
  height: 100%;
  /* 确保图表在白色背景下正确显示 */
  color: #303133;
}
</style>

import request from '@/utils/request'
import type { Arithmetic } from '@/types/api'
import type { AxiosRequestHeaders } from 'axios'

/**
 * 获取算法列表
 */
export const getArithmeticList = (params: {
  page?: number
  pageSize?: number
  type?: number
  product_key?: string
  name?: string
}): Promise<{
  success: boolean
  data: {
    total: number
    list: Arithmetic[]
  }
}> => {
  return request({
    url: '/arithmetics',
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取算法详情
 */
export const getArithmeticDetail = (
  id: string
): Promise<{
  success: boolean
  data: Arithmetic
}> => {
  return request({
    url: `/arithmetics/${id}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 创建算法
 */
export const createArithmetic = (data: {
  type: number
  name: string
  product_key: string
  content?: string
  oss_path?: string
  md5?: string
  is_default?: boolean
}): Promise<{
  success: boolean
  data: Arithmetic
}> => {
  return request({
    url: '/arithmetics',
    method: 'post',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 更新算法
 */
export const updateArithmetic = (
  id: string,
  data: {
    type?: number
    name?: string
    product_key?: string
    content?: string
    oss_path?: string
    md5?: string
    is_default?: boolean
  }
): Promise<{
  success: boolean
  data: Arithmetic
}> => {
  return request({
    url: `/arithmetics/${id}`,
    method: 'put',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 删除算法
 */
export const deleteArithmetic = (
  id: string
): Promise<{
  success: boolean
  message: string
}> => {
  return request({
    url: `/arithmetics/${id}`,
    method: 'delete',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 下载算法
 */
export const downloadArithmetic = (
  arithmeticId: string,
  deviceId: string
): Promise<{
  success: boolean
  data: {
    name: string
    encryptedContent: string
    type: number
  }
}> => {
  return request({
    url: `/arithmetics/download/${arithmeticId}`,
    method: 'get',
    params: { deviceId },
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取算法暴露的方法列表
 * @param id 算法ID
 */
export const getAlgorithmMethods = (id: string): Promise<{
  success: boolean
  data: string[]
}> => {
  return request({
    url: `/arithmetics/${id}/methods`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 执行算法方法
 * @param id 算法ID
 * @param methodName 方法名
 * @param params 参数
 */
export const executeAlgorithmMethod = (
  id: string, 
  methodName: string, 
  params: any
): Promise<{
  success: boolean
  data: any
}> => {
  return request({
    url: `/arithmetics/${id}/execute`,
    method: 'post',
    data: {
      methodName,
      params
    },
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 批量执行多个算法方法
 * @param id 算法ID
 * @param methods 方法列表，每个方法包含methodName和params
 */
export const executeMultipleAlgorithmMethods = (
  id: string,
  methods: Array<{
    methodName: string
    params?: any
  }>
): Promise<{
  success: boolean
  data: Array<{
    methodName: string
    result: any
  }>
}> => {
  return request({
    url: `/arithmetics/${id}/execute-batch`,
    method: 'post',
    data: {
      methods
    },
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 链式执行算法方法
 * 将上一个方法的返回结果作为下一个方法的输入参数
 * @param id 算法ID
 * @param methods 方法名称数组
 * @param params 初始参数，用于第一个方法的调用
 */
export const executeChainedAlgorithmMethods = (
  id: string,
  methods: string[],
  params?: any
): Promise<{
  success: boolean
  data: any // 返回最后一个方法的执行结果
}> => {
  return request({
    url: `/arithmetics/${id}/execute-chain`,
    method: 'post',
    data: {
      methods,
      params
    },
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取算法运行状态
 * @param id 算法ID (可选, 如果不传则获取当前加载算法的状态)
 */
export const getAlgorithmStatus = (
  id?: string
): Promise<{
  success: boolean
  data: {
    loaded: boolean
    algorithmId?: number
    algorithmName?: string
    md5?: string
    loadTime?: string
    memory?: {
      used: number
      total: number
      usagePercentage: number
      heapStatistics: {
        totalHeapSize: number
        totalHeapSizeExecutable: number
        totalPhysicalSize: number
        usedHeapSize: number
        heapSizeLimit: number
      }
    }
    message?: string
    error?: string
  }
}> => {
  return request({
    url: id ? `/arithmetics/${id}/status` : '/arithmetics/status',
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

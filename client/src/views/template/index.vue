<!--
  模板列表页面 - 包含 transform: scale() 居中解决方案

  核心功能：
  1. 4列瀑布流布局展示模板卡片
  2. 动态高度的模板预览，支持缩放显示
  3. 完美解决 transform: scale() 元素的居中问题

  居中方案核心原理：
  transform: scale() 只改变元素的视觉效果，不改变布局空间，导致 flexbox 无法正确居中

  解决方案：三层结构
  1. template-preview: 外层容器，使用 flexbox 居中
  2. preview-wrapper: 包装器，提供缩放后的正确占用空间
  3. preview-canvas: 画板，应用 transform: scale() 缩放

  关键函数：
  - getWrapperStyle(): 计算缩放后的实际显示尺寸
  - getCanvasStyle(): 应用缩放变换，保持原始尺寸

  适用场景：
  - 任何需要缩放显示且要求居中的内容
  - 动态尺寸的预览组件
  - 响应式布局中的缩放元素
-->
<template>
  <div class="template-container">
    <!-- 搜索和筛选区域 -->
    <el-card class="filter-card">
      <div class="filter-section">
        <el-row :gutter="24">
          <el-col :span="3">
            <el-input
              v-model="searchKeyword"
              placeholder="模板名称"
              clearable
            />
          </el-col>
          <el-col :span="3">
            <el-select
              v-model="statusFilter"
              placeholder="状态筛选"
              clearable
            >
              <el-option
                label="全部"
                value=""
              />
              <el-option
                label="启用"
                :value="1"
              />
              <el-option
                label="禁用"
                :value="0"
              />
            </el-select>
          </el-col>
          <el-col
            :span="3"
            class="filter-buttons"
          >
            <el-button
              type="primary"
              @click="debouncedHandleSearch"
            >
              搜索
            </el-button>
            <el-button @click="resetForm">
              清空
            </el-button>
          </el-col>
          <el-col :span="10">
            <!-- 分页信息和页码导航 -->
            <div class="pagination-info">
              <el-pagination
                background
                layout="total, sizes, prev, pager, next"
                :total="total"
                :current-page="currentPage"
                :page-size="pageSize"
                :page-sizes="[10, 20, 30, 40, 50]"
                small
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-col>
          <el-col
            :span="5"
            style="text-align: right; display: flex; align-items: center; justify-content: flex-end; height: 40px;"
          >
            <el-button
              type="primary"
              @click="handleAddTemplate"
            >
              <el-icon><Plus /></el-icon>新增模板
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>
      
    <!-- 4列瀑布流模板卡片展示 -->
    <div
      ref="waterfallContainer"
      v-loading="loading"
      class="template-waterfall"
    >
      <div
        v-for="template in templates"
        :key="template.id"
        class="template-card"
        :class="{ 'template-card-disabled': template.status === 0 }"
      >
        <!-- 卡片头部 -->
        <div class="template-card-header">
          <h3 class="template-name">
            {{ template.name }}
          </h3>
          <el-tag
            :type="template.status === 1 ? 'success' : 'info'"
            size="small"
          >
            {{ template.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </div>

        <!-- 模板预览区域 -->
        <div class="template-preview-container">
          <!--
              三层结构解决 transform: scale() 居中问题：
              1. template-preview: 外层容器，使用 flexbox 居中
              2. preview-wrapper: 包装器，提供缩放后的正确占用空间
              3. preview-canvas: 画板，应用 transform: scale() 缩放

              核心原理：transform: scale() 不改变元素的布局空间，只改变视觉效果
              所以需要包装器来提供正确的占用空间，让 flexbox 能正确计算居中位置
            -->
          <div
            class="template-preview"
            :style="{ height: getPreviewHeight(template) + 'px' }"
          >
            <!-- 预览包装器：计算缩放后的实际显示尺寸，为 flexbox 居中提供正确的占用空间 -->
            <div
              class="preview-wrapper"
              :style="getWrapperStyle(template)"
            >
              <!-- 预览画板：应用缩放变换，保持原始尺寸但视觉上缩放 -->
              <div
                class="preview-canvas"
                :style="getCanvasStyle(template)"
              >
                <!-- 预览组件 -->
                <div
                  v-for="(component, index) in getTemplateComponents(template)"
                  :key="'preview-' + (component.id || index)"
                  class="preview-component"
                  :style="getComponentStyle(component)"
                >
                  <div class="preview-component-content">
                    <div class="preview-component-header">
                      <span class="preview-component-title">{{ component.name || component.title || '组件' }}</span>
                      <el-tag
                        size="small"
                        type="info"
                      >
                        {{ component.type || 'chart' }}
                      </el-tag>
                    </div>
                    <div class="preview-component-body">
                      <!-- 如果有缩略图就显示缩略图 -->
                      <div
                        v-if="component.thumbnail"
                        class="preview-component-chart"
                      >
                        <img
                          :src="component.thumbnail"
                          :alt="component.name"
                          @error="handleImageError"
                        >
                      </div>
                      <!-- 否则显示占位符 -->
                      <div
                        v-else
                        class="preview-component-placeholder"
                      >
                        <el-icon class="preview-icon">
                          <TrendCharts />
                        </el-icon>
                        <p>{{ component.type || 'Chart' }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div
                  v-if="getTemplateComponents(template).length === 0"
                  class="preview-empty"
                >
                  <el-icon class="preview-empty-icon">
                    <DocumentCopy />
                  </el-icon>
                  <p>暂无组件</p>
                  <span class="preview-empty-tip">点击编辑添加图表组件</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div
          v-if="template.description"
          class="template-card-content"
        >
          <p class="template-description">
            {{ template.description || '暂无描述' }}
          </p>
        </div>

        <!-- 卡片操作 -->
        <div class="template-card-footer">
          <el-button
            type="success"
            link
            @click="handleConfigDevice(template)"
          >
            <el-icon><Connection /></el-icon>
            绑定设备({{ template.deviceCount || 0 }})
          </el-button>
          <el-button
            type="primary"
            link
            @click="handleEdit(template)"
          >
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button
            type="primary"
            link
            @click="handleView(template)"
          >
            <el-icon><View /></el-icon>
            查看
          </el-button>
          <el-dropdown
            trigger="click"
            @command="command => handleCommand(command, template)"
          >
            <el-button
              type="primary"
              link
            >
              <el-icon><More /></el-icon>
              更多
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="copy">
                  <el-icon><DocumentCopy /></el-icon>
                  复制
                </el-dropdown-item>
                <el-dropdown-item :command="template.status === 1 ? 'disable' : 'enable'">
                  <el-icon>
                    <CircleClose v-if="template.status === 1" />
                    <CircleCheck v-else />
                  </el-icon>
                  {{ template.status === 1 ? '禁用' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item
                  command="delete"
                  divided
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
        
      <!-- 空状态展示 -->
      <el-empty
        v-if="templates.length === 0 && !loading"
        description="暂无模板数据"
        class="empty-data"
      />
    </div>

    <!-- 查看模板详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="模板详情"
      width="600px"
    >
      <div
        v-if="currentTemplate"
        class="template-detail"
      >
        <div class="detail-item">
          <span class="detail-label">名称：</span>
          <span>{{ currentTemplate.name }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">描述：</span>
          <span>{{ currentTemplate.description || '暂无描述' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">状态：</span>
          <el-tag
            :type="currentTemplate.status === 1 ? 'success' : 'info'"
            size="small"
          >
            {{ currentTemplate.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </div>
        <div class="detail-item">
          <span class="detail-label">创建时间：</span>
          <span>{{ formatDate(currentTemplate.created_at) }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">最后修改：</span>
          <span>{{ currentTemplate.modified_at ? formatDate(currentTemplate.modified_at) : '无修改记录' }}</span>
        </div>
        <div class="detail-item config-item">
          <span class="detail-label">配置：</span>
          <pre>{{ formatConfig(currentTemplate.config) }}</pre>
        </div>
      </div>
    </el-dialog>

    <!-- 配置设备对话框 -->
    <el-dialog
      v-model="deviceConfigDialogVisible"
      title="配置设备"
      width="60%"
      :before-close="() => { deviceConfigDialogVisible = false }"
    >
      <div v-if="currentConfigTemplate">
        <div class="device-selection">
          <div class="device-search">
            <el-input
              v-model="deviceSearchKeyword"
              placeholder="搜索设备名称或序列号"
              clearable
              @input="handleDeviceSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div
            v-loading="deviceListLoading"
            class="device-list"
          >
            <el-checkbox-group v-model="selectedDeviceIds">
              <div
                v-for="device in filteredDevices"
                :key="device.id"
                class="device-item"
              >
                <el-checkbox
                  :label="device.id"
                >
                  <div class="device-info">
                    <div class="device-name">
                      {{ device.deviceName }}
                    </div>
                    <div class="device-details">
                      <span class="device-serial">序列号: {{ device.serialNumber }}</span>
                      <span
                        v-if="device.hasTemplate"
                        class="device-status"
                        :class="{ 'current-template': device.isCurrentTemplate }"
                      >
                        {{ device.isCurrentTemplate ? '已配置此模板' : '已配置其他模板' }}
                      </span>
                    </div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>

            <el-empty
              v-if="!deviceListLoading && filteredDevices.length === 0"
              description="暂无设备数据"
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deviceConfigDialogVisible = false">
            取消
          </el-button>
          <el-button
            type="danger"
            :loading="unbindSubmitting"
            :disabled="selectedDeviceIds.length === 0 || !hasSelectedBoundDevices"
            @click="handleUnbindDevices"
          >
            解除绑定
          </el-button>
          <el-button
            type="primary"
            :loading="configSubmitting"
            @click="handleSubmitDeviceConfig"
          >
            确认绑定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Edit, View, More, TrendCharts, DocumentCopy, Search, Connection, CircleClose, CircleCheck, Delete } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { getTemplateList, deleteTemplate, updateTemplateStatus, copyTemplate } from '@/api/template'
import { getDeviceList } from '@/api/device'
import { addDeviceTemplateConfig, getDeviceTemplateConfigs, getTemplateDeviceCount, unbindDeviceTemplate } from '@/api/deviceTemplateConfig'
import { debouncedResize, debouncedApiCall } from '@/utils/debounce'

// 状态数据
const loading = ref(false)
const templates = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const statusFilter = ref('')
const viewDialogVisible = ref(false)
const currentTemplate = ref(null)
const router = useRouter()

// 设备配置相关状态
const deviceConfigDialogVisible = ref(false)
const currentConfigTemplate = ref(null)
const deviceListLoading = ref(false)
const configSubmitting = ref(false)
const unbindSubmitting = ref(false)
const deviceSearchKeyword = ref('')
const selectedDeviceIds = ref([])
const allDevices = ref([])
const filteredDevices = ref([])

// 计算属性：是否有选中的已绑定设备
const hasSelectedBoundDevices = computed(() => {
  return selectedDeviceIds.value.some(deviceId => {
    const device = allDevices.value.find(d => d.id === deviceId)
    return device && device.isCurrentTemplate
  })
})

// 瀑布流相关
const waterfallContainer = ref(null)
const isLayouting = ref(false) // 防止重复布局

// 瀑布流布局函数 - 优化版本
const initWaterfall = async () => {
  // 防止重复执行
  if (isLayouting.value) return
  isLayouting.value = true

  try {
    // 等待DOM更新完成
    await nextTick()

    // 额外等待一小段时间，确保所有内容（包括图片）都渲染完成
    await new Promise(resolve => setTimeout(resolve, 100))

    if (!waterfallContainer.value) return

    const container = waterfallContainer.value
    const cards = container.querySelectorAll('.template-card')

    if (cards.length === 0) return

    // 响应式列数设置，充分利用容器宽度
    const containerWidth = container.offsetWidth
    let columns = 4 // 默认4列

    // 响应式调整列数
    if (containerWidth < 480) {
      columns = 1
    } else if (containerWidth < 768) {
      columns = 2
    } else if (containerWidth < 1200) {
      columns = 3
    } else {
      columns = 4
    }

    const gap = 20 // 间距

    // 计算每个卡片的宽度，充分利用容器宽度
    const cardWidth = (containerWidth - (columns - 1) * gap) / columns

    // 初始化列高度数组
    const columnHeights = new Array(columns).fill(0)

    // 设置容器为相对定位
    container.style.position = 'relative'
    container.style.width = '100%'

    // 先重置所有卡片的样式，确保能获取到正确的高度
    cards.forEach((card) => {
      card.style.position = 'static'
      card.style.width = cardWidth + 'px'
      card.style.left = 'auto'
      card.style.top = 'auto'
    })

    // 等待重置后的重新渲染
    await nextTick()

    // 为每个卡片设置位置
    cards.forEach((card) => {
      // 找到最短的列
      const shortestColumnIndex = columnHeights.indexOf(Math.min(...columnHeights))

      // 计算位置
      const x = shortestColumnIndex * (cardWidth + gap)
      const y = columnHeights[shortestColumnIndex]

      // 设置卡片样式
      card.style.position = 'absolute'
      card.style.left = x + 'px'
      card.style.top = y + 'px'
      card.style.width = cardWidth + 'px'
      card.style.transition = 'all 0.3s ease'

      // 获取卡片的实际高度（包括内容）
      const cardHeight = card.offsetHeight

      // 更新列高度
      columnHeights[shortestColumnIndex] += cardHeight + gap
    })

    // 设置容器高度
    const maxHeight = Math.max(...columnHeights)
    container.style.height = maxHeight + 'px'
  } finally {
    isLayouting.value = false
  }
}

// 使用统一的防抖布局函数
const debouncedInitWaterfall = debouncedResize(() => {
  initWaterfall()
})

// 监听模板数据变化，重新布局
watch(templates, (newTemplates, oldTemplates) => {
  // 只有在模板数量或内容发生实际变化时才重新布局
  if (!oldTemplates || newTemplates.length !== oldTemplates.length) {
    nextTick(() => {
      initWaterfall()
    })
  }
}, { deep: false }) // 改为浅层监听，提高性能

// 初始化加载数据
onMounted(() => {
  fetchTemplates()

  // 监听窗口大小变化，使用防抖
  window.addEventListener('resize', debouncedInitWaterfall)
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', debouncedInitWaterfall)

  // 清理防抖函数
  if (debouncedInitWaterfall && typeof debouncedInitWaterfall.cancel === 'function') {
    debouncedInitWaterfall.cancel()
  }
  if (debouncedHandleSearch && typeof debouncedHandleSearch.cancel === 'function') {
    debouncedHandleSearch.cancel()
  }
})

// 获取模板列表数据
const fetchTemplates = async () => {
  loading.value = true
  try {
    const params = {
      pageSize: pageSize.value,
      page: currentPage.value
    }

    // 添加搜索条件
    if (searchKeyword.value) {
      params.name = searchKeyword.value
    }

    // 添加状态筛选
    if (statusFilter.value !== '') {
      params.status = statusFilter.value
    }

    const response = await getTemplateList(params)
    if (response.success) {
      // 为每个模板获取设备绑定数量 - 添加并发控制，避免过多同时请求
      const batchSize = 5 // 每批处理5个模板
      const templatesWithDeviceCount = []

      for (let i = 0; i < response.data.list.length; i += batchSize) {
        const batch = response.data.list.slice(i, i + batchSize)
        const batchResults = await Promise.all(
          batch.map(async (template) => {
            try {
              const countResponse = await getTemplateDeviceCount(template.id)
              return {
                ...template,
                deviceCount: countResponse.success ? countResponse.data.count : 0
              }
            } catch (error) {
              console.error(`获取模板 ${template.id} 设备数量失败:`, error)
              return {
                ...template,
                deviceCount: 0
              }
            }
          })
        )
        templatesWithDeviceCount.push(...batchResults)
      }

      templates.value = templatesWithDeviceCount
      total.value = response.data.total

      // 数据加载完成后，等待DOM更新并初始化瀑布流布局
      await nextTick()
      // 额外延迟，确保所有模板预览内容都渲染完成
      setTimeout(() => {
        initWaterfall()
      }, 200)
    } else {
      ElMessage.error(response.message || '获取模板列表失败')
    }
  } catch (error) {
    console.error('获取模板列表错误:', error)
    ElMessage.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

// 强制重新布局（用于解决布局问题）
const forceRelayout = async () => {
  isLayouting.value = false // 重置布局状态
  await nextTick()
  setTimeout(() => {
    initWaterfall()
  }, 100)
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchTemplates()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchTemplates()
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchTemplates()
}

// 使用防抖的搜索函数
const debouncedHandleSearch = debouncedApiCall(() => {
  currentPage.value = 1
  fetchTemplates()
})

// 重置表单
const resetForm = () => {
  searchKeyword.value = ''
  statusFilter.value = ''
  currentPage.value = 1
  fetchTemplates()
}

// 新增模板
const handleAddTemplate = () => {
  router.push('/template/create')
}

// 编辑模板
const handleEdit = (template) => {
  router.push(`/template/edit/${template.id}`)
}

// 查看模板详情
const handleView = (template) => {
  currentTemplate.value = template
  viewDialogVisible.value = true
}

// 复制模板
const handleCopyTemplate = async (template) => {
  try {
    // 弹出输入框让用户输入新模板名称
    const { value: newName } = await ElMessageBox.prompt(
      '请输入新模板的名称',
      '复制模板',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: `${template.name}_复制`,
        inputValidator: (value) => {
          if (!value || value.trim() === '') {
            return '模板名称不能为空'
          }
          if (value.length > 64) {
            return '模板名称不能超过64个字符'
          }
          return true
        }
      }
    )

    if (newName && newName.trim()) {
      // 调用复制API
      const response = await copyTemplate(template.id, newName.trim())

      if (response.success) {
        ElMessage.success('模板复制成功')
        // 刷新模板列表
        fetchTemplates()
      } else {
        ElMessage.error(response.message || '复制模板失败')
      }
    }
  } catch (error) {
    // 用户取消操作或其他错误
    if (error !== 'cancel') {
      console.error('复制模板错误:', error)
      ElMessage.error('复制模板失败')
    }
  }
}

// 更多操作处理
const handleCommand = (command, template) => {
  switch (command) {
    case 'copy':
      handleCopyTemplate(template)
      break
    case 'enable':
      changeTemplateStatus(template, 1)
      break
    case 'disable':
      changeTemplateStatus(template, 0)
      break
    case 'delete':
      confirmDeleteTemplate(template)
      break
    default:
      break
  }
}

// 修改模板状态
const changeTemplateStatus = async (template, status) => {
  try {
    // 如果是禁用操作且模板有绑定设备，不允许禁用
    if (status === 0 && template.deviceCount > 0) {
      await ElMessageBox.alert(
        `模板 "${template.name}" 当前绑定了 ${template.deviceCount} 个设备，无法直接禁用。请先解除所有设备的绑定关系，然后再禁用模板。`,
        '无法禁用',
        {
          confirmButtonText: '我知道了',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )
      return // 不执行禁用操作
    }

    // 执行状态更新
    const response = await updateTemplateStatus(template.id, status)
    if (response.success) {
      ElMessage.success(status === 1 ? '模板已启用' : '模板已禁用')
      fetchTemplates()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    // 如果是用户取消操作，不显示错误信息
    if (error === 'cancel') {
      return
    }
    console.error('修改模板状态错误:', error)
    ElMessage.error('操作失败')
  }
}

// 确认删除模板
const confirmDeleteTemplate = async (template) => {
  try {
    // 检查模板是否有绑定设备
    if (template.deviceCount > 0) {
      // 有绑定设备，不允许删除
      await ElMessageBox.alert(
        `模板 "${template.name}" 当前绑定了 ${template.deviceCount} 个设备，无法直接删除。请先解除所有设备的绑定关系，然后再删除模板。`,
        '无法删除',
        {
          confirmButtonText: '我知道了',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )
      return // 不执行删除操作
    }

    // 没有绑定设备，确认删除
    await ElMessageBox.confirm(
      `确认删除模板 "${template.name}" 吗？删除后将无法恢复，请谨慎操作。`,
      '删除确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    // 用户确认删除，执行删除操作
    handleDeleteTemplate(template.id)
  } catch (error) {
    // 用户取消操作或关闭对话框，不执行任何操作
    if (error === 'cancel' || error === 'close') {
      return
    }
    console.error('删除确认错误:', error)
  }
}

// 删除模板
const handleDeleteTemplate = async (id) => {
  try {
    const response = await deleteTemplate(id)
    if (response.success) {
      ElMessage.success('模板已删除')

      // 如果当前页没有数据了，且不是第一页，则跳转到前一页
      if (templates.value.length === 1 && currentPage.value > 1) {
        currentPage.value--
      }

      await fetchTemplates()
      // 删除后强制重新布局
      forceRelayout()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    console.error('删除模板错误:', error)
    ElMessage.error('删除失败')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化配置JSON
const formatConfig = (configString) => {
  try {
    if (!configString) return '{}'
    const config = JSON.parse(configString)
    return JSON.stringify(config, null, 2)
  } catch (error) {
    return configString || '{}'
  }
}

// 获取模板组件列表
const getTemplateComponents = (template) => {
  try {
    if (!template.config) return []

    const config = JSON.parse(template.config)
    let components = config.components || []

    // 标准化组件数据结构，确保正确解析位置和尺寸
    components = components.map((comp, index) => {
      // 解析组件的config字段获取缩略图
      let thumbnail = null
      try {
        if (comp.config && typeof comp.config === 'string') {
          const configObj = JSON.parse(comp.config)
          thumbnail = configObj.thumbnail
        }
      } catch (error) {
        console.warn('解析组件config失败:', error)
      }

      const standardized = {
        id: comp.id || `comp-${index}`,
        name: comp.name || comp.title || comp.label || `组件${index + 1}`,
        type: comp.type || comp.chartType || 'chart',
        // 正确解析位置信息
        x: comp.position?.x ?? comp.x ?? 0,
        y: comp.position?.y ?? comp.y ?? 0,
        // 正确解析尺寸信息
        width: comp.size?.width ?? comp.width ?? 200,
        height: comp.size?.height ?? comp.height ?? 150,
        // 获取缩略图 - 优先从config中解析的thumbnail
        thumbnail: thumbnail || comp.thumbnail || comp.image || comp.chartImage || comp.preview
      }

      return standardized
    })
    return components
  } catch (error) {
    console.error('解析模板配置失败:', error, template.config)
    return []
  }
}



// 计算预览高度
const getPreviewHeight = (template) => {
  const components = getTemplateComponents(template)

  if (components.length === 0) {
    return 200
  }

  // 计算实际内容的边界
  let maxBottom = 0
  let maxRight = 0

  components.forEach(component => {
    const x = component.x || 0
    const y = component.y || 0
    const width = component.width || 200
    const height = component.height || 150

    maxRight = Math.max(maxRight, x + width)
    maxBottom = Math.max(maxBottom, y + height)
  })

  // 如果没有有效的组件位置，使用默认高度
  if (maxBottom === 0) {
    return 250
  }

  // 计算缩放比例，确保内容完全可见
  const cardWidth = 280 // 卡片内容区域宽度
  const scaleX = cardWidth / Math.max(maxRight, 800) // 基于宽度的缩放
  const scaleY = scaleX // 保持比例一致

  // 计算缩放后的高度
  const scaledHeight = maxBottom * scaleY

  // 添加一些内边距，确保内容不会贴边
  const paddedHeight = scaledHeight + 40

  // 限制高度范围：最小200px，最大600px
  return Math.min(Math.max(paddedHeight, 200), 600)
}

/**
 * 获取预览包装器样式 - 解决 transform: scale() 居中问题的关键函数
 *
 * 问题背景：
 * transform: scale() 只改变元素的视觉效果，不改变元素在布局中的占用空间
 * 这导致 flexbox 的 justify-content: center 基于原始尺寸计算，无法正确居中缩放后的内容
 *
 * 解决方案：
 * 通过包装器提供缩放后的正确占用空间，让 flexbox 能基于实际显示尺寸进行居中计算
 *
 * @param {Object} template - 模板对象
 * @returns {Object} 包装器的 CSS 样式对象
 */
const getWrapperStyle = (template) => {
  const components = getTemplateComponents(template)

  // 空模板的默认处理
  if (components.length === 0) {
    const scale = 0.35                    // 默认缩放比例
    const originalWidth = 800             // 原始画板宽度
    const scaledWidth = originalWidth * scale  // 缩放后的实际显示宽度

    return {
      width: scaledWidth + 'px',          // 关键：设置缩放后的实际宽度
      height: (600 * scale) + 'px',       // 关键：设置缩放后的实际高度
      position: 'relative'
    }
  }

  // 计算所有组件的边界，确定内容的实际尺寸
  let maxRight = 0
  let maxBottom = 0

  components.forEach(component => {
    const x = component.x || 0
    const y = component.y || 0
    const width = component.width || 200
    const height = component.height || 150

    maxRight = Math.max(maxRight, x + width)
    maxBottom = Math.max(maxBottom, y + height)
  })

  // 计算缩放比例和最终的显示尺寸
  const actualWidth = Math.max(maxRight, 800)      // 内容的实际宽度
  const actualHeight = Math.max(maxBottom, 600)    // 内容的实际高度
  const scale = Math.min(1, 300 / actualWidth)     // 计算合适的缩放比例

  return {
    width: (actualWidth * scale) + 'px',   // 关键：缩放后的实际显示宽度
    height: (actualHeight * scale) + 'px', // 关键：缩放后的实际显示高度
    position: 'relative'
  }
}

/**
 * 获取预览画板样式 - 应用缩放变换
 *
 * 功能说明：
 * 1. 保持元素的原始尺寸（width/height）
 * 2. 通过 transform: scale() 进行视觉缩放
 * 3. 配合包装器实现完美居中效果
 *
 * 注意事项：
 * - 必须保持原始尺寸，缩放只通过 transform 实现
 * - transformOrigin 设置为 'top left' 确保缩放基准点一致
 * - 缩放比例必须与包装器中的计算保持一致
 *
 * @param {Object} template - 模板对象
 * @returns {Object} 画板的 CSS 样式对象
 */
const getCanvasStyle = (template) => {
  const components = getTemplateComponents(template)

  // 空模板的默认处理
  if (components.length === 0) {
    return {
      width: '800px',                     // 保持原始宽度
      height: '600px',                    // 保持原始高度
      transform: 'scale(0.35)',           // 应用缩放变换（与包装器中的 scale 一致）
      transformOrigin: 'top left',        // 缩放基准点：左上角
      position: 'relative'
    }
  }

  // 计算内容边界，确定画板的原始尺寸
  let maxRight = 0
  let maxBottom = 0

  components.forEach(component => {
    const x = component.x || 0
    const y = component.y || 0
    const width = component.width || 200
    const height = component.height || 150

    maxRight = Math.max(maxRight, x + width)
    maxBottom = Math.max(maxBottom, y + height)
  })

  // 计算缩放比例（必须与包装器中的计算保持一致）
  const actualWidth = Math.max(maxRight, 800)
  const scale = Math.min(1, 300 / actualWidth)

  return {
    width: actualWidth + 'px',            // 保持原始宽度
    height: Math.max(maxBottom, 600) + 'px', // 保持原始高度
    transform: `scale(${scale})`,         // 应用缩放变换
    transformOrigin: 'top left',          // 缩放基准点：左上角
    position: 'relative'
  }
}

// 获取组件样式
const getComponentStyle = (component) => {
  // 使用标准化后的属性
  const x = component.x || 0
  const y = component.y || 0
  const width = component.width || 200
  const height = component.height || 150

  return {
    position: 'absolute',
    left: x + 'px',
    top: y + 'px',
    width: width + 'px',
    height: height + 'px'
  }
}

// 处理图片加载错误
const handleImageError = (event) => {
  console.log('图片加载失败:', event.target.src)
  // 可以在这里设置默认图片或隐藏图片
}

// 配置设备处理函数
const handleConfigDevice = async (template) => {
  currentConfigTemplate.value = template
  selectedDeviceIds.value = []
  deviceSearchKeyword.value = ''
  deviceConfigDialogVisible.value = true

  // 加载设备列表
  await fetchDeviceList()
}

// 获取设备列表
const fetchDeviceList = async () => {
  deviceListLoading.value = true
  try {
    const response = await getDeviceList({
      page: 1,
      pageSize: 1000 // 获取所有设备
    })

    if (response.success) {
      // 获取每个设备的模板配置状态
      const devicesWithConfig = await Promise.all(
        response.data.list.map(async (device) => {
          try {
            const configResponse = await getDeviceTemplateConfigs(device.id)
            const hasCurrentTemplate = configResponse.success &&
              configResponse.data.list.some(config =>
                config.template_id === currentConfigTemplate.value.id
              )
            const hasOtherTemplate = configResponse.success &&
              configResponse.data.list.some(config =>
                config.template_id !== currentConfigTemplate.value.id
              )

            return {
              ...device,
              hasTemplate: hasCurrentTemplate || hasOtherTemplate,
              isCurrentTemplate: hasCurrentTemplate
            }
          } catch (error) {
            console.error(`获取设备 ${device.id} 配置失败:`, error)
            return {
              ...device,
              hasTemplate: false,
              isCurrentTemplate: false
            }
          }
        })
      )

      allDevices.value = devicesWithConfig
      filteredDevices.value = devicesWithConfig

      // 预选已配置此模板的设备
      selectedDeviceIds.value = devicesWithConfig
        .filter(device => device.isCurrentTemplate)
        .map(device => device.id)
    } else {
      ElMessage.error(response.message || '获取设备列表失败')
    }
  } catch (error) {
    console.error('获取设备列表错误:', error)
    ElMessage.error('获取设备列表失败')
  } finally {
    deviceListLoading.value = false
  }
}

// 设备搜索处理
const handleDeviceSearch = () => {
  const keyword = deviceSearchKeyword.value.toLowerCase()
  if (!keyword) {
    filteredDevices.value = allDevices.value
  } else {
    filteredDevices.value = allDevices.value.filter(device =>
      device.deviceName.toLowerCase().includes(keyword) ||
      device.serialNumber.toLowerCase().includes(keyword)
    )
  }
}

// 提交设备配置
const handleSubmitDeviceConfig = async () => {
  if (selectedDeviceIds.value.length === 0) {
    ElMessage.warning('请至少选择一个设备')
    return
  }

  configSubmitting.value = true
  try {
    // 为每个选中的设备添加模板配置
    const promises = selectedDeviceIds.value.map(deviceId =>
      addDeviceTemplateConfig(deviceId, currentConfigTemplate.value.id)
    )

    const results = await Promise.allSettled(promises)

    // 检查结果
    const successCount = results.filter(result =>
      result.status === 'fulfilled' && result.value.success
    ).length

    const failCount = results.length - successCount

    if (successCount > 0) {
      ElMessage.success(`成功绑定 ${successCount} 个设备${failCount > 0 ? `，${failCount} 个设备绑定失败` : ''}`)
      deviceConfigDialogVisible.value = false
      // 刷新模板列表以更新设备数量
      fetchTemplates()
    } else {
      ElMessage.error('所有设备绑定失败')
    }
  } catch (error) {
    console.error('绑定设备错误:', error)
    ElMessage.error('绑定设备失败')
  } finally {
    configSubmitting.value = false
  }
}

// 解除设备绑定
const handleUnbindDevices = async () => {
  // 筛选出已绑定当前模板的设备
  const boundDeviceIds = selectedDeviceIds.value.filter(deviceId => {
    const device = allDevices.value.find(d => d.id === deviceId)
    return device && device.isCurrentTemplate
  })

  if (boundDeviceIds.length === 0) {
    ElMessage.warning('请选择已绑定的设备')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要解除 ${boundDeviceIds.length} 个设备与此模板的绑定吗？`,
      '确认解除绑定',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    unbindSubmitting.value = true

    // 为每个已绑定的设备解除绑定
    const promises = boundDeviceIds.map(deviceId =>
      unbindDeviceTemplate(deviceId, currentConfigTemplate.value.id)
    )

    const results = await Promise.allSettled(promises)

    // 检查结果
    const successCount = results.filter(result =>
      result.status === 'fulfilled' && result.value.success
    ).length

    const failCount = results.length - successCount

    if (successCount > 0) {
      ElMessage.success(`成功解除 ${successCount} 个设备的绑定${failCount > 0 ? `，${failCount} 个设备解除失败` : ''}`)
      deviceConfigDialogVisible.value = false
      // 刷新模板列表以更新设备数量
      fetchTemplates()
    } else {
      ElMessage.error('所有设备解除绑定失败')
    }
  } catch (error) {
    if (error === 'cancel') {
      return // 用户取消操作
    }
    console.error('解除绑定错误:', error)
    ElMessage.error('解除绑定失败')
  } finally {
    unbindSubmitting.value = false
  }
}
</script>

<style scoped>
.template-container {
  padding: 24px;
  background-color: var(--apple-background);
}

/* 筛选区域卡片 */
.filter-card {
  margin-bottom: 24px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 0;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  height: 40px; /* 与分页信息高度保持一致 */
}

/* 分页信息样式 */
.pagination-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 40px; /* 增加高度以匹配其他组件 */
}

.pagination-info .el-pagination {
  padding: 0;
  margin: 0;
}

/* 确保整个筛选区域的所有列都垂直居中 */
.filter-section .el-row {
  align-items: center;
}

.filter-section .el-col {
  display: flex;
  align-items: center;
}

/* JavaScript驱动的瀑布流布局 */
.template-waterfall {
  position: relative;
  width: 100%;
  margin-bottom: 30px;
  min-height: 300px;
}

.template-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* JavaScript会动态设置position: absolute, left, top, width */
}

.template-card:hover {
  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-4px);
}

/* 禁用状态的模板卡片样式 */
.template-card-disabled {
  opacity: 0.6;
  background-color: #f5f7fa !important;
}

.template-card-disabled .template-card-header {
  background-color: #f0f2f5;
}

.template-card-disabled .template-name {
  color: #909399 !important;
}

.template-card-disabled .template-preview-container {
  background-color: #f0f2f5 !important;
}

.template-card-disabled:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  transform: none;
}

.template-card-header {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 模板预览区域 */
.template-preview-container {
  position: relative;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  overflow: hidden;
}

/*
 * 三层结构解决 transform: scale() 居中问题的 CSS 样式
 *
 * 层级结构：
 * .template-preview (外层容器)
 *   └── .preview-wrapper (包装器)
 *       └── .preview-canvas (画板)
 */

/* 外层容器：负责 flexbox 居中布局 */
.template-preview {
  position: relative;
  background: white;
  width: 100%;
  overflow: hidden;
  display: flex;                    /* 启用 flexbox 布局 */
  align-items: flex-start;          /* 垂直对齐：顶部对齐 */
  justify-content: center;          /* 水平对齐：居中 - 关键属性 */
}

/* 包装器：提供缩放后的正确占用空间，让 flexbox 能正确计算居中位置 */
.preview-wrapper {
  position: relative;
  background: white;
  /*
   * 重要：尺寸由 getWrapperStyle() 动态计算
   * 宽高 = 原始尺寸 × 缩放比例
   * 这样 flexbox 就能基于正确的尺寸进行居中计算
   */
}

/* 画板：应用 transform: scale() 缩放变换 */
.preview-canvas {
  background: white;
  position: relative;
  min-height: 100px;
  /*
   * 重要：保持原始尺寸，只通过 transform: scale() 进行视觉缩放
   * 样式由 getCanvasStyle() 动态计算
   */
}

.preview-component {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 1;
  min-width: 50px;
  min-height: 50px;
  transition: all 0.2s ease;
}

.preview-component:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.preview-component-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  font-size: 10px;
}

.preview-component-title {
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 4px;
}

.preview-component-body {
  flex: 1;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.preview-component-chart {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.preview-component-chart img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 2px;
}

.preview-component-placeholder {
  text-align: center;
  color: #909399;
  font-size: 10px;
}

.preview-icon {
  font-size: 16px;
  margin-bottom: 4px;
  color: #c0c4cc;
}

.preview-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #909399;
  font-size: 12px;
}

.preview-empty-icon {
  font-size: 32px;
  margin-bottom: 8px;
  color: #c0c4cc;
}

.preview-empty-tip {
  font-size: 10px;
  color: #a8abb2;
  margin-top: 4px;
}

.template-card-content {
  padding: 15px;
  flex: 1;
  border-bottom: 1px solid #f0f0f0;
}

.template-description {
  margin: 0 0 12px;
  color: #606266;
  font-size: 14px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 60px;
}

.template-meta {
  color: #909399;
  font-size: 12px;
}

.template-card-footer {
  padding: 10px 15px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}



/* 模板详情样式 */
.template-detail {
  padding: 10px;
}

.detail-item {
  margin-bottom: 15px;
}

.detail-label {
  font-weight: 600;
  color: #303133;
  margin-right: 10px;
}

.config-item {
  display: flex;
  flex-direction: column;
}

.config-item pre {
  margin-top: 10px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
}

/* 空状态样式 */
.empty-data {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  margin: 20px 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .filter-buttons {
    text-align: left;
    margin-top: 16px;
  }

  .template-card {
    margin-bottom: 0;
  }
}

/* 设备配置对话框样式 */
.config-template-info {
  margin-bottom: 20px;
}

.config-template-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.config-template-info p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.device-selection h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.device-search {
  margin-bottom: 20px;
}

.device-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.device-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.device-item:last-child {
  border-bottom: none;
}

.device-info {
  margin-left: 8px;
}

.device-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.device-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.device-serial {
  font-size: 12px;
  color: #909399;
}

.device-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: #f56c6c;
  color: white;
}

.device-status.current-template {
  background-color: #67c23a;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
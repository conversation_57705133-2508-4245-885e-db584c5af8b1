import * as acorn from 'acorn'
import * as walk from 'acorn-walk'

/**
 * 代码安全分析结果接口
 */
export interface SecurityAnalysisResult {
  isSecure: boolean
  violations: SecurityViolation[]
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  score: number // 0-100, 100为最安全
}

/**
 * 安全违规接口
 */
export interface SecurityViolation {
  type: string
  message: string
  line?: number
  column?: number
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  code?: string
}

/**
 * 代码安全分析器
 * 使用AST解析器进行深度代码安全分析
 */
export class CodeSecurityAnalyzer {
  private readonly FORBIDDEN_IDENTIFIERS = [
    'eval', 'setTimeout', 'setInterval', 'setImmediate',
    'require', 'import', 'process', 'global', 'globalThis', 'window',
    'Buffer', 'fs', 'path', 'os', 'child_process', 'cluster',
    'crypto', 'http', 'https', 'net', 'dgram', 'dns', 'vm',
    'worker_threads', 'inspector', 'repl', 'readline'
  ]

  private readonly FORBIDDEN_PROPERTIES = [
    'constructor', '__proto__', 'prototype', '__defineGetter__',
    '__defineSetter__', '__lookupGetter__', '__lookupSetter__'
  ]

  private readonly DANGEROUS_PATTERNS = [
    // 动态代码执行模式
    /this\s*\[\s*['"`]constructor['"`]\s*\]/gi,
    /this\s*\[\s*['"`]__proto__['"`]\s*\]/gi,
    /Function\s*\(\s*['"`]return\s+this['"`]\s*\)/gi,
    /\[\s*['"`]eval['"`]\s*\]/gi,
    /\[\s*['"`]Function['"`]\s*\]/gi,
    // 字符串拼接执行
    /['"`]\s*\+\s*['"`].*eval/gi,
    /['"`]\s*\+\s*['"`].*Function/gi,
    // 编码绕过
    /String\.fromCharCode/gi,
    /atob|btoa/gi,
    /unescape|decodeURI/gi
  ]

  /**
   * 分析代码安全性
   * @param code 要分析的代码
   * @returns 安全分析结果
   */
  public analyzeCode(code: string): SecurityAnalysisResult {
    const violations: SecurityViolation[] = []
    
    try {
      // 1. 基础字符串模式检查
      this.checkDangerousPatterns(code, violations)
      
      // 2. AST深度分析
      this.performASTAnalysis(code, violations)
      
      // 3. 计算风险等级和分数
      const result = this.calculateRiskAssessment(violations)
      
      return {
        isSecure: violations.length === 0,
        violations,
        riskLevel: result.riskLevel,
        score: result.score
      }
    } catch (error: any) {
      // 如果AST解析失败，可能是恶意代码
      violations.push({
        type: 'PARSE_ERROR',
        message: `代码解析失败，可能包含恶意语法: ${error.message}`,
        severity: 'HIGH'
      })
      
      return {
        isSecure: false,
        violations,
        riskLevel: 'HIGH',
        score: 0
      }
    }
  }

  /**
   * 检查危险模式
   */
  private checkDangerousPatterns(code: string, violations: SecurityViolation[]): void {
    this.DANGEROUS_PATTERNS.forEach(pattern => {
      const matches = code.match(pattern)
      if (matches) {
        matches.forEach(match => {
          violations.push({
            type: 'DANGEROUS_PATTERN',
            message: `检测到危险代码模式: ${match}`,
            severity: 'HIGH',
            code: match
          })
        })
      }
    })
  }

  /**
   * 执行AST分析
   */
  private performASTAnalysis(code: string, violations: SecurityViolation[]): void {
    let ast: acorn.Node
    
    try {
      // 首先尝试作为脚本解析
      ast = acorn.parse(code, {
        ecmaVersion: 2022,
        sourceType: 'script',
        locations: true
      })
    } catch (scriptError: any) {
      try {
        // 如果脚本解析失败，尝试作为模块解析
        ast = acorn.parse(code, {
          ecmaVersion: 2022,
          sourceType: 'module',
          locations: true
        })
      } catch (moduleError: any) {
        throw new Error(`AST解析失败: ${moduleError.message}`)
      }
    }

    // 遍历AST节点
    walk.full(ast, (node: any) => {
      this.analyzeNode(node, violations)
    })
  }

  /**
   * 分析AST节点
   */
  private analyzeNode(node: any, violations: SecurityViolation[]): void {
    switch (node.type) {
      case 'Identifier':
        this.checkForbiddenIdentifier(node, violations)
        break
      case 'MemberExpression':
        this.checkMemberExpression(node, violations)
        break
      case 'CallExpression':
        this.checkCallExpression(node, violations)
        break
      case 'NewExpression':
        this.checkNewExpression(node, violations)
        break
      case 'AssignmentExpression':
        this.checkAssignmentExpression(node, violations)
        break
      case 'BinaryExpression':
        this.checkBinaryExpression(node, violations)
        break
    }
  }

  /**
   * 检查禁止的标识符
   */
  private checkForbiddenIdentifier(node: any, violations: SecurityViolation[]): void {
    // 跳过函数声明和函数表达式中的function关键字
    if (node.name === 'function') {
      return // function关键字是合法的，不应该被禁止
    }

    if (this.FORBIDDEN_IDENTIFIERS.includes(node.name)) {
      violations.push({
        type: 'FORBIDDEN_IDENTIFIER',
        message: `使用了禁止的标识符: ${node.name}`,
        line: node.loc?.start.line,
        column: node.loc?.start.column,
        severity: 'CRITICAL',
        code: node.name
      })
    }
  }

  /**
   * 检查成员表达式
   */
  private checkMemberExpression(node: any, violations: SecurityViolation[]): void {
    // 检查属性访问
    if (node.property && node.property.type === 'Identifier') {
      if (this.FORBIDDEN_PROPERTIES.includes(node.property.name)) {
        violations.push({
          type: 'FORBIDDEN_PROPERTY',
          message: `访问了危险属性: ${node.property.name}`,
          line: node.loc?.start.line,
          column: node.loc?.start.column,
          severity: 'HIGH'
        })
      }
    }

    // 检查动态属性访问
    if (node.computed && node.property) {
      if (node.property.type === 'Literal' && typeof node.property.value === 'string') {
        const propName = node.property.value
        if (this.FORBIDDEN_IDENTIFIERS.includes(propName) ||
            this.FORBIDDEN_PROPERTIES.includes(propName)) {
          violations.push({
            type: 'DYNAMIC_FORBIDDEN_ACCESS',
            message: `通过动态访问使用禁止的属性: ${propName}`,
            line: node.loc?.start.line,
            column: node.loc?.start.column,
            severity: 'CRITICAL'
          })
        }
      }

      // 检查二元表达式拼接的动态访问 (如 this['ev' + 'al'])
      if (node.property.type === 'BinaryExpression' && node.property.operator === '+') {
        const left = node.property.left
        const right = node.property.right

        if (left.type === 'Literal' && right.type === 'Literal' &&
            typeof left.value === 'string' && typeof right.value === 'string') {
          const combined = left.value + right.value
          if (this.FORBIDDEN_IDENTIFIERS.includes(combined)) {
            violations.push({
              type: 'DYNAMIC_FORBIDDEN_ACCESS',
              message: `通过字符串拼接动态访问禁止的属性: ${combined}`,
              line: node.loc?.start.line,
              column: node.loc?.start.column,
              severity: 'CRITICAL'
            })
          }
        }
      }
    }
  }

  /**
   * 检查函数调用表达式
   */
  private checkCallExpression(node: any, violations: SecurityViolation[]): void {
    // 检查直接调用禁止的函数
    if (node.callee.type === 'Identifier' &&
        this.FORBIDDEN_IDENTIFIERS.includes(node.callee.name)) {
      violations.push({
        type: 'FORBIDDEN_CALL',
        message: `调用了禁止的函数: ${node.callee.name}`,
        line: node.loc?.start.line,
        column: node.loc?.start.column,
        severity: 'CRITICAL'
      })
    }

    // 特别检查Function构造函数调用
    if (node.callee.type === 'Identifier' && node.callee.name === 'Function') {
      violations.push({
        type: 'FORBIDDEN_CALL',
        message: `调用了禁止的Function构造函数`,
        line: node.loc?.start.line,
        column: node.loc?.start.column,
        severity: 'CRITICAL'
      })
    }

    // 检查构造函数调用
    if (node.callee.type === 'MemberExpression' &&
        node.callee.property &&
        node.callee.property.name === 'constructor') {
      violations.push({
        type: 'CONSTRUCTOR_CALL',
        message: '尝试通过constructor访问构造函数',
        line: node.loc?.start.line,
        column: node.loc?.start.column,
        severity: 'HIGH'
      })
    }
  }

  /**
   * 检查new表达式
   */
  private checkNewExpression(node: any, violations: SecurityViolation[]): void {
    if (node.callee.type === 'Identifier' &&
        this.FORBIDDEN_IDENTIFIERS.includes(node.callee.name)) {
      violations.push({
        type: 'FORBIDDEN_NEW',
        message: `使用new创建禁止的对象: ${node.callee.name}`,
        line: node.loc?.start.line,
        column: node.loc?.start.column,
        severity: 'CRITICAL'
      })
    }

    // 特别检查new Function()
    if (node.callee.type === 'Identifier' && node.callee.name === 'Function') {
      violations.push({
        type: 'FORBIDDEN_NEW',
        message: `使用new创建禁止的Function对象`,
        line: node.loc?.start.line,
        column: node.loc?.start.column,
        severity: 'CRITICAL'
      })
    }
  }

  /**
   * 检查赋值表达式
   */
  private checkAssignmentExpression(node: any, violations: SecurityViolation[]): void {
    // 检查是否尝试修改全局对象
    if (node.left.type === 'MemberExpression' && 
        node.left.object.type === 'Identifier' && 
        ['global', 'globalThis', 'window'].includes(node.left.object.name)) {
      violations.push({
        type: 'GLOBAL_MODIFICATION',
        message: '尝试修改全局对象',
        line: node.loc?.start.line,
        column: node.loc?.start.column,
        severity: 'HIGH'
      })
    }
  }

  /**
   * 检查二元表达式
   */
  private checkBinaryExpression(node: any, violations: SecurityViolation[]): void {
    // 检查字符串拼接可能的代码注入
    if (node.operator === '+' && 
        node.left.type === 'Literal' && 
        node.right.type === 'Literal' &&
        typeof node.left.value === 'string' && 
        typeof node.right.value === 'string') {
      const combined = node.left.value + node.right.value
      if (this.FORBIDDEN_IDENTIFIERS.some(id => combined.includes(id))) {
        violations.push({
          type: 'STRING_CONCAT_INJECTION',
          message: '字符串拼接可能构造危险代码',
          line: node.loc?.start.line,
          column: node.loc?.start.column,
          severity: 'MEDIUM'
        })
      }
    }
  }

  /**
   * 计算风险评估
   */
  private calculateRiskAssessment(violations: SecurityViolation[]): {
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    score: number
  } {
    if (violations.length === 0) {
      return { riskLevel: 'LOW', score: 100 }
    }

    let totalRisk = 0
    const severityWeights = {
      'LOW': 1,
      'MEDIUM': 3,
      'HIGH': 7,
      'CRITICAL': 15
    }

    violations.forEach(violation => {
      totalRisk += severityWeights[violation.severity]
    })

    // 计算分数 (0-100)
    const score = Math.max(0, 100 - totalRisk * 2)

    // 确定风险等级
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    if (violations.some(v => v.severity === 'CRITICAL')) {
      riskLevel = 'CRITICAL'
    } else if (violations.some(v => v.severity === 'HIGH')) {
      riskLevel = 'HIGH'
    } else if (violations.some(v => v.severity === 'MEDIUM')) {
      riskLevel = 'MEDIUM'
    } else {
      riskLevel = 'LOW'
    }

    return { riskLevel, score }
  }
}

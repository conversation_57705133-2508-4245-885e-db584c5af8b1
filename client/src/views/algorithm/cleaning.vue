<template>
  <div class="algorithm-container">
    <el-card class="algorithm-header">
      <div class="header-content">
        <div class="algorithm-title">
          <h2>
            <el-tag effect="light">
              默认
            </el-tag>
            {{ defaultAlgorithm ? defaultAlgorithm.name : '暂无默认清洗算法' }}
          </h2>
          <div class="algorithm-info">
            {{
              defaultAlgorithm
                ? '应用时间: ' + (defaultAlgorithm.modifiedAt || defaultAlgorithm.createdAt)
                : ''
            }}
          </div>
        </div>
        <el-button
          type="primary"
          class="switch-button"
          @click="openSwitchDialog"
        >
          切换
        </el-button>
      </div>
    </el-card>

    <!-- 搜索和列表整合在一个卡片中 -->
    <el-card
      v-loading="loading"
      class="algorithm-list-card"
    >
      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="24">
          <el-col :span="4">
            <el-input
              v-model="filterForm.name"
              placeholder="算法名称"
              clearable
            />
          </el-col>
          <el-col
            :span="4"
            class="filter-buttons"
          >
            <el-button
              type="primary"
              @click="debouncedHandleSearch"
            >
              搜索
            </el-button>
            <el-button @click="resetForm">
              清空
            </el-button>
          </el-col>
          <el-col
            :span="16"
            class="action-buttons"
          >
            <el-button
              type="primary"
              @click="addNewAlgorithm"
            >
              新增算法
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 算法列表 -->
      <el-table
        :data="algorithmList"
        style="width: 100%"
        border
      >
        <el-table-column
          prop="name"
          label="算法名称"
          align="center"
        />
        <el-table-column
          prop="usedNum"
          label="应用设备数量"
          align="center"
        />
        <el-table-column
          prop="updateTime"
          label="最后修改时间"
          align="center"
        />
        <el-table-column
          label="状态"
          align="center"
        >
          <template #default="scope">
            <el-tag
              v-if="scope.row.isDefault"
              type="success"
            >
              默认
            </el-tag>
            <span v-else>-</span>
            <el-tag
              v-if="scope.row.status && scope.row.status.loaded"
              type="success"
              class="ml-2"
            >
              已加载
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="360"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="editAlgorithm(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="primary"
              link
              @click="copyAlgorithm(scope.row)"
            >
              复制
            </el-button>
            <el-button
              type="primary"
              link
              @click="applyAlgorithm(scope.row)"
            >
              应用
            </el-button>
            <el-button
              type="primary" 
              link
              :loading="scope.row.statusLoading"
              @click="fetchAlgorithmStatus(scope.row)"
            >
              状态
              <el-popover
                v-if="scope.row.status"
                placement="top"
                :width="300"
                trigger="hover"
              >
                <template #reference>
                  <el-icon class="ml-2">
                    <InfoFilled />
                  </el-icon>
                </template>
                <div v-if="scope.row.status.loaded">
                  <p><strong>算法ID:</strong> {{ scope.row.status.algorithmId }}</p>
                  <p><strong>加载时间:</strong> {{ formatDate(scope.row.status.loadTime) }}</p>
                  <p v-if="scope.row.status.memory">
                    <strong>内存使用:</strong> 
                    {{ scope.row.status.memory.used }}MB / {{ scope.row.status.memory.total }}MB
                    ({{ scope.row.status.memory.usagePercentage }}%)
                  </p>
                  <el-progress 
                    :percentage="scope.row.status.memory?.usagePercentage || 0"
                    :status="getMemoryStatus(scope.row.status.memory?.usagePercentage)"
                  />
                </div>
                <div v-else>
                  <p>{{ scope.row.status.message || '算法未加载' }}</p>
                  <p
                    v-if="scope.row.status.error"
                    class="error-message"
                  >
                    {{ scope.row.status.error }}
                  </p>
                </div>
              </el-popover>
            </el-button>
            <el-button
              type="danger"
              link
              @click="deleteAlgorithm(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页部分 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增算法对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="新增算法"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form
        :model="algorithmForm"
        label-width="100px"
      >
        <el-form-item
          label="算法名称"
          required
        >
          <el-input
            v-model="algorithmForm.name"
            placeholder="请输入算法名称"
            maxlength="32"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="算法类型">
          <el-select
            v-model="algorithmForm.type"
            disabled
          >
            <el-option
              label="清洗算法"
              :value="0"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="submitAlgorithm"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 切换默认算法对话框 -->
    <el-dialog
      v-model="switchDialogVisible"
      title="切换默认算法"
      width="50%"
    >
      <div v-loading="switchLoading">
        <el-table
          :data="availableAlgorithms"
          style="width: 100%"
          border
          highlight-current-row
        >
          <el-table-column
            prop="name"
            label="算法名称"
          />
          <el-table-column
            prop="updateTime"
            label="最后修改时间"
          />
          <el-table-column
            label="当前状态"
            width="100"
          >
            <template #default="scope">
              <el-tag
                v-if="scope.row.isDefault"
                type="success"
              >
                默认
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="120"
            align="center"
          >
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                :disabled="scope.row.isDefault"
                @click="setAsDefault(scope.row)"
              >
                设为默认
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="switchDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 应用算法对话框 -->
    <ApplyAlgorithmDialog
      v-model:visible="applyDialogVisible"
      :algorithm-id="currentAlgorithm.id"
      :algorithm-name="currentAlgorithm.name"
      :algorithm-type="0"
      @success="handleApplySuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import {
  getArithmeticList,
  getArithmeticDetail,
  createArithmetic,
  updateArithmetic,
  deleteArithmetic,
  getAlgorithmStatus
} from '@/api'
import ApplyAlgorithmDialog from '@/components/ApplyAlgorithmDialog.vue'
import { debouncedApiCall } from '@/utils/debounce'

const router = useRouter()
const defaultAlgorithm = ref(null)

const filterForm = reactive({
  name: '',
  status: ''
})

// 算法列表数据
const algorithmList = ref([])
const loading = ref(false)
const submitLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'edit'
const algorithmForm = reactive({
  id: null,
  name: '',
  description: '',
  type: 0, // 清洗算法类型为0
  isPublished: false
})

// 算法切换对话框
const switchDialogVisible = ref(false)
const switchLoading = ref(false)
const availableAlgorithms = ref([])

// 应用算法对话框
const applyDialogVisible = ref(false)
const currentAlgorithm = ref({})

// 获取默认清洗算法
const fetchDefaultAlgorithm = async () => {
  try {
    loading.value = true

    // 查询默认清洗算法
    const params = {
      type: 0, // 清洗算法
      is_default: true
    }

    const response = await getArithmeticList(params)

    if (response.success && response.data.list && response.data.list.length > 0) {
      // 获取默认算法详情
      const defaultArithmeticId = response.data.list[0].id
      const detailResponse = await getArithmeticDetail(defaultArithmeticId)

      if (detailResponse.success) {
        defaultAlgorithm.value = detailResponse.data
      }
    }
  } catch (error) {
    console.error('获取默认清洗算法失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取算法列表
const fetchAlgorithms = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      type: 0, // 清洗算法类型为0
      name: filterForm.name
    }

    const response = await getArithmeticList(params)

    if (response.success) {
      algorithmList.value = response.data.list.map(item => ({
        id: item.id,
        name: item.name,
        usedNum: item.usedDeviceCount || '0', // 使用后端返回的应用设备数量
        updateTime: item.modifiedAt || item.createdAt,
        product_key: item.productKey,
        isDefault: item.isDefault,
        status: null, // 初始化状态为null
        statusLoading: false // 初始化状态加载标志为false
      }))
      total.value = response.data.total
    } else {
      ElMessage.error('获取算法列表失败')
    }
  } catch (error) {
    console.error('获取算法列表失败:', error)
    ElMessage.error('获取算法列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 使用防抖的搜索函数
const debouncedHandleSearch = debouncedApiCall(() => {
  currentPage.value = 1
  fetchAlgorithms()
})

const resetForm = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = ''
  })
  currentPage.value = 1
  fetchAlgorithms()
}

const handleCurrentChange = val => {
  currentPage.value = val
  fetchAlgorithms()
}

const addNewAlgorithm = () => {
  dialogType.value = 'add'
  Object.keys(algorithmForm).forEach(key => {
    if (key !== 'isPublished' && key !== 'type') {
      algorithmForm[key] = key === 'id' ? null : ''
    } else if (key === 'type') {
      algorithmForm[key] = 0 // 清洗算法类型为0
    } else {
      algorithmForm[key] = false
    }
  })
  dialogVisible.value = true
}

// 提交新增算法
const submitAlgorithm = async () => {
  // 验证表单
  if (!algorithmForm.name) {
    ElMessage.warning('请输入算法名称')
    return
  }

  try {
    submitLoading.value = true

    // 创建算法对象
    const newAlgorithm = {
      name: algorithmForm.name,
      type: 0, // 清洗算法类型为0
      content: '',
      is_default: false // 新创建的不是默认算法
    }

    // 调用创建API
    const response = await createArithmetic(newAlgorithm)

    if (response.success) {
      ElMessage.success('新增算法成功')
      dialogVisible.value = false
      fetchAlgorithms() // 刷新列表

      // 如果需要，可以直接跳转到编辑页面
      if (response.data && response.data.id) {
        router.push({
          path: `/algorithm/edit/${response.data.id}`,
          query: {
            type: '0',
            name: algorithmForm.name
          }
        })
      }
    } else {
      ElMessage.error('新增算法失败')
    }
  } catch (error) {
    console.error('新增算法失败:', error)
    ElMessage.error('新增算法失败: ' + (error.message || '未知错误'))
  } finally {
    submitLoading.value = false
  }
}

const editAlgorithm = row => {
  // 跳转到算法编辑页面
  router.push({
    path: `/algorithm/edit/${row.id}`,
    query: {
      type: '0',
      name: row.name
    }
  })
}

const copyAlgorithm = async row => {
  try {
    // 获取要复制的算法详情
    const response = await getArithmeticDetail(row.id)

    if (response.success) {
      // 创建新算法（复制）
      const newAlgorithm = {
        type: 0, // 清洗算法
        name: `${row.name}_复制`,
        content: response.data.content,
        md5: response.data.md5,
        is_default: false // 复制的不设为默认
      }

      const createResponse = await createArithmetic(newAlgorithm)

      if (createResponse.success) {
        ElMessage.success('复制算法成功')
        fetchAlgorithms() // 刷新列表
      } else {
        ElMessage.error('复制算法失败')
      }
    } else {
      ElMessage.error('获取算法详情失败')
    }
  } catch (error) {
    console.error('复制算法失败:', error)
    ElMessage.error('复制算法失败: ' + (error.message || '未知错误'))
  }
}

const applyAlgorithm = row => {
  currentAlgorithm.value = row
  applyDialogVisible.value = true
}

// 应用成功回调
const handleApplySuccess = () => {
  // 刷新算法列表数据，更新应用设备数量
  fetchAlgorithms()
}

const deleteAlgorithm = row => {
  if (row.isDefault) {
    ElMessage.warning('默认算法不能删除')
    return
  }

  ElMessageBox.confirm(`确定要删除算法 "${row.name}" 吗？此操作不可恢复。`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        const response = await deleteArithmetic(row.id)

        if (response.success) {
          ElMessage.success('删除成功')
          fetchAlgorithms() // 刷新列表
        } else {
          ElMessage.error('删除失败')
        }
      } catch (error) {
        console.error('删除算法失败:', error)
        ElMessage.error('删除失败: ' + (error.message || '未知错误'))
      }
    })
    .catch(() => {
      // 取消操作
    })
}

// 打开切换算法对话框
const openSwitchDialog = async () => {
  switchDialogVisible.value = true
  await fetchAvailableAlgorithms()
}

// 获取可用算法列表（当前设备类型的所有算法）
const fetchAvailableAlgorithms = async () => {
  try {
    switchLoading.value = true

    // 获取清洗算法列表
    const params = {
      type: 0, // 清洗算法类型为0
      page: 1,
      pageSize: 100 // 获取足够多的算法
    }

    const response = await getArithmeticList(params)

    if (response.success) {
      availableAlgorithms.value = response.data.list.map(item => ({
        id: item.id,
        name: item.name,
        updateTime: item.modifiedAt || item.createdAt,
        isDefault: item.isDefault
      }))
    } else {
      ElMessage.error('获取可用算法列表失败')
    }
  } catch (error) {
    console.error('获取可用算法列表失败:', error)
    ElMessage.error('获取可用算法列表失败: ' + (error.message || '未知错误'))
  } finally {
    switchLoading.value = false
  }
}

// 设置为默认算法
const setAsDefault = async algorithm => {
  try {
    switchLoading.value = true

    // 将选中的算法设为默认
    const response = await updateArithmetic(algorithm.id, { is_default: true })

    if (response.success) {
      ElMessage.success(`已将 "${algorithm.name}" 设为默认算法`)

      // 更新算法列表和默认算法显示
      await fetchDefaultAlgorithm()
      await fetchAvailableAlgorithms()
      switchDialogVisible.value = false // 设置成功后关闭对话框

      // 刷新主列表
      fetchAlgorithms()
    } else {
      ElMessage.error('设置默认算法失败')
    }
  } catch (error) {
    console.error('设置默认算法失败:', error)
    ElMessage.error('设置默认算法失败: ' + (error.message || '未知错误'))
  } finally {
    switchLoading.value = false
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', { 
      year: 'numeric', 
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (e) {
    return dateStr
  }
}

// 根据内存使用率获取状态类型
const getMemoryStatus = (percentage) => {
  if (!percentage && percentage !== 0) return ''
  if (percentage >= 80) return 'exception'
  if (percentage >= 60) return 'warning'
  return 'success'
}

// 获取算法运行状态
const fetchAlgorithmStatus = async (row) => {
  try {
    row.statusLoading = true
    const response = await getAlgorithmStatus(row.id)
    if (response.success) {
      row.status = response.data
    } else {
      ElMessage.error('获取算法状态失败')
    }
  } catch (error) {
    console.error('获取算法状态失败:', error)
    ElMessage.error('获取算法状态失败: ' + (error.message || '未知错误'))
  } finally {
    row.statusLoading = false
  }
}

onMounted(async () => {
  // 先获取默认算法
  await fetchDefaultAlgorithm()
  // 然后加载算法列表
  fetchAlgorithms()
})

// 组件卸载时清理防抖函数
onUnmounted(() => {
  if (debouncedHandleSearch && typeof debouncedHandleSearch.cancel === 'function') {
    debouncedHandleSearch.cancel()
  }
})
</script>

<style scoped>
.algorithm-container {
  padding: 20px;
}

.algorithm-header {
  margin-bottom: 20px;
  background-color: #409eff;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.algorithm-title {
  display: flex;
  justify-content: start;
  align-items: baseline;
}

.algorithm-info {
  margin-left: 10px;
  font-size: 12px;
}

.switch-button {
  margin-left: auto;
}

.algorithm-list-card {
  margin-bottom: 24px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-buttons {
  text-align: left;
}

.action-buttons {
  text-align: right;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-buttons {
    text-align: left;
    margin-top: 16px;
  }
}

.ml-2 {
  margin-left: 8px;
}

.error-message {
  color: #f56c6c;
}
</style>

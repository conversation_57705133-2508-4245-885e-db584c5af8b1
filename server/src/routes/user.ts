import { Router } from 'express'
import {
  login,
  register,
  getProfile,
  updateProfile,
  logout,
  getCurrentUser,
  dingTalkLogin
} from '../controllers/user'
import { verifyToken } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'
import { validateLogin, validateRegister } from '../middleware/validation'

const router = Router()

// 认证路由（添加输入验证）
router.post('/login', validateLogin, asyncHandler(login))
router.post('/register', validateRegister, asyncHandler(register))
router.post('/dingtalk-login', asyncHandler(dingTalkLogin))

// 用户信息路由（需要认证）
router.get('/profile', asyncHandler(verifyToken), asyncHandler(getProfile))
router.put('/profile', asyncHandler(verifyToken), asyncHandler(updateProfile))

// 登出路由
router.post('/logout', asyncHandler(verifyToken), asyncHandler(logout))

// 获取当前用户信息路由 (需要验证令牌)
router.get('/me', asyncHandler(verifyToken), asyncHandler(getCurrentUser))

export default router

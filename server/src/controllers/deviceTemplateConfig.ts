import { Response, NextFunction, Request } from 'express'
import { AppError } from '../middleware/error'
import { DeviceTemplateConfig, Device, Template } from '../models'
import sequelize from '../config/database'

/**
 * 获取设备的模板配置列表
 */
export const getDeviceTemplateConfigs = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params
    
    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400)
    }
    
    // 检查设备是否存在
    const device = await Device.findByPk(parseInt(deviceId, 10))
    if (!device) {
      throw new AppError('设备不存在', 404)
    }
    
    // 查询设备的模板配置
    const configs = await DeviceTemplateConfig.findAll({
      where: { device_id: parseInt(deviceId, 10) },
      include: [
        {
          model: Template,
          as: 'template',
          attributes: ['id', 'name', 'config', 'description', 'status']
        }
      ],
      order: [['id', 'ASC']]
    })
    
    res.status(200).json({
      success: true,
      data: {
        total: configs.length,
        list: configs
      }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 获取设备启用的模板列表
 */
export const getDeviceEnabledTemplates = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params
    
    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400)
    }
    
    // 检查设备是否存在
    const device = await Device.findByPk(parseInt(deviceId, 10))
    if (!device) {
      throw new AppError('设备不存在', 404)
    }
    
    // 使用静态方法获取设备启用的模板
    const result = await DeviceTemplateConfig.getEnabledTemplatesByDeviceId(parseInt(deviceId, 10))
    
    if (!result.success) {
      throw new AppError(result.message || '获取设备启用模板失败', 500)
    }
    
    res.status(200).json(result)
  } catch (error) {
    next(error)
  }
}

/**
 * 添加设备模板配置
 */
export const addDeviceTemplateConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params
    const { templateId } = req.body
    
    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400)
    }
    
    if (!templateId) {
      throw new AppError('模板ID不能为空', 400)
    }
    
    // 检查设备是否存在
    const device = await Device.findByPk(parseInt(deviceId, 10))
    if (!device) {
      throw new AppError('设备不存在', 404)
    }
    
    // 检查模板是否存在
    const template = await Template.findByPk(parseInt(templateId, 10))
    if (!template) {
      throw new AppError('模板不存在', 404)
    }
    
    // 启动事务处理设备模板配置
    const transaction = await sequelize.transaction()

    try {
      // 检查是否已存在相同配置
      const existingConfig = await DeviceTemplateConfig.findOne({
        where: {
          device_id: parseInt(deviceId, 10),
          template_id: parseInt(templateId, 10)
        },
        transaction
      })

      if (existingConfig) {
        // 如果已存在相同配置，直接返回成功（无需更新）
        await transaction.commit()

        res.status(200).json({
          success: true,
          data: existingConfig,
          message: '设备已配置此模板'
        })
        return
      }

      // 检查设备是否已有其他模板配置
      const existingOtherConfig = await DeviceTemplateConfig.findOne({
        where: {
          device_id: parseInt(deviceId, 10)
        },
        transaction
      })

      let resultConfig
      let isReplacement = false

      if (existingOtherConfig) {
        // 如果设备已有其他模板配置，更新现有记录
        await existingOtherConfig.update({
          template_id: parseInt(templateId, 10),
          modified_at: new Date()
        }, { transaction })

        resultConfig = existingOtherConfig
        isReplacement = true
      } else {
        // 创建新的设备模板配置
        resultConfig = await DeviceTemplateConfig.create({
          device_id: parseInt(deviceId, 10),
          template_id: parseInt(templateId, 10),
          created_at: new Date(),
          modified_at: new Date()
        }, { transaction })
      }

      await transaction.commit()

      res.status(200).json({
        success: true,
        data: resultConfig,
        message: isReplacement ? '设备模板配置已更新' : '设备模板配置成功'
      })
    } catch (error) {
      await transaction.rollback()
      throw error
    }
  } catch (error) {
    next(error)
  }
}

/**
 * 删除设备模板配置
 */
export const deleteDeviceTemplateConfig = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    
    if (!id) {
      throw new AppError('配置ID不能为空', 400)
    }
    
    // 检查配置是否存在
    const config = await DeviceTemplateConfig.findByPk(parseInt(id, 10))
    if (!config) {
      throw new AppError('配置不存在', 404)
    }
    
    // 删除配置
    await config.destroy()
    
    res.status(200).json({
      success: true,
      message: '删除设备模板配置成功'
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 批量配置设备模板
 */
export const batchConfigDeviceTemplates = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params
    const { templateIds } = req.body
    
    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400)
    }
    
    if (!templateIds || !Array.isArray(templateIds) || templateIds.length === 0) {
      throw new AppError('模板ID列表不能为空', 400)
    }
    
    // 检查设备是否存在
    const device = await Device.findByPk(parseInt(deviceId, 10))
    if (!device) {
      throw new AppError('设备不存在', 404)
    }
    
    // 启动事务
    const transaction = await sequelize.transaction()
    
    try {
      // 删除该设备的所有现有模板配置
      await DeviceTemplateConfig.destroy({
        where: { device_id: parseInt(deviceId, 10) },
        transaction
      })
      
      // 批量创建新的模板配置
      const configsToCreate = templateIds.map(templateId => ({
        device_id: parseInt(deviceId, 10),
        template_id: parseInt(templateId.toString(), 10),
        created_at: new Date()
      }))
      
      await DeviceTemplateConfig.bulkCreate(configsToCreate, { transaction })
      
      // 提交事务
      await transaction.commit()
      
      res.status(200).json({
        success: true,
        message: '批量配置设备模板成功'
      })
    } catch (error) {
      // 回滚事务
      await transaction.rollback()
      throw error
    }
  } catch (error) {
    next(error)
  }
}

/**
 * 获取模板绑定的设备数量
 */
export const getTemplateDeviceCount = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { templateId } = req.params

    if (!templateId) {
      throw new AppError('模板ID不能为空', 400)
    }

    // 统计绑定此模板的设备数量
    const count = await DeviceTemplateConfig.count({
      where: {
        template_id: parseInt(templateId, 10)
      }
    })

    res.json({
      success: true,
      data: { count }
    })
  } catch (error) {
    next(error)
  }
}

/**
 * 解除设备与模板的绑定
 */
export const unbindDeviceTemplate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId, templateId } = req.params

    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400)
    }

    if (!templateId) {
      throw new AppError('模板ID不能为空', 400)
    }

    // 查找要删除的配置记录
    const config = await DeviceTemplateConfig.findOne({
      where: {
        device_id: parseInt(deviceId, 10),
        template_id: parseInt(templateId, 10)
      }
    })

    if (!config) {
      throw new AppError('设备与模板的绑定关系不存在', 404)
    }

    // 删除绑定记录
    await config.destroy()

    res.json({
      success: true,
      message: '成功解除设备与模板的绑定'
    })
  } catch (error) {
    next(error)
  }
}
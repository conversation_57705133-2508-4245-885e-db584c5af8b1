import { Router } from 'express'
import userRoutes from './user'
import deviceRoutes from './device'
import fileRoutes from './file'
import arithmeticRoutes from './arithmetic'
import exampleRoutes from './example'
import importRoutes from './import'
import deviceArithmeticRoutes from './deviceArithmetic'
import dataRoutes from './data'
import chartComponentRoutes from './chartComponent'
import deviceChartConfigRoutes from './deviceChartComponentConfig'
import templateRoutes from './template'
import deviceTemplateConfigRoutes from './deviceTemplateConfig'

const router = Router()

// 健康检查路由
router.get('/health', (req, res) => {
  res.json({ status: 'ok', message: '服务器运行正常' })
})

// 用户相关路由
router.use('/api/users', userRoutes)

// 设备相关路由
router.use('/api/devices', deviceRoutes)

// 文件相关路由
router.use('/api/files', fileRoutes)

// 算法相关路由
router.use('/api/arithmetics', arithmeticRoutes)

// 示例路由
router.use('/api/example', exampleRoutes)

// 导入数据路由
router.use('/api/imports', importRoutes)

// 设备算法关联路由
router.use('/api/device-arithmetics', deviceArithmeticRoutes)

// 数据相关路由
router.use('/api/data', dataRoutes)

// 图表组件路由
router.use('/api/chart-components', chartComponentRoutes)

// 设备图表组件配置路由
router.use('/api/device-chart-configs', deviceChartConfigRoutes)

// 模板路由
router.use('/api/templates', templateRoutes)

// 设备模板配置路由
router.use('/api/device-template-configs', deviceTemplateConfigRoutes)

// 可以在这里添加更多的路由模块

export default router

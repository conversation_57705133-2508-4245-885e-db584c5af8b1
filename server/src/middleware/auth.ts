import jwt from 'jsonwebtoken'
import { Response, NextFunction } from 'express'
import { AuthRequest, AuthMiddleware, JwtPayload } from '../types/middleware'
import config from '../config'
import User from '../models/user'
import { AppError } from '../middleware/error'

/**
 * 验证 JWT 令牌的中间件（简化版，确保所有错误都能被捕获）
 */
export const verifyToken: AuthMiddleware = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  // 优先从Cookie获取token，其次从Authorization头获取（向后兼容）
  const token = req.cookies?.token || req.headers['authorization']?.split(' ')[1]

  // 如果没有token，直接返回错误
  if (!token) {
    return next(new AppError('未提供认证令牌，请登录后重试', 401, -10086))
  }

  // 验证token格式（基本长度检查）
  if (token.length < 20) {
    return next(new AppError('令牌格式错误', 401, -10086))
  }

  try {
    // 验证token并获取解码后的payload
    const decoded = jwt.verify(token, config.jwtSecret, {
      algorithms: ['HS256'],
      issuer: 'web-panel',
      audience: 'web-panel-client'
    }) as JwtPayload

    // 检查token是否过期
    if (decoded.exp && decoded.exp < Math.floor(Date.now() / 1000)) {
      return next(new AppError('令牌已过期', 401, -10086))
    }

    // 检查是否有用户ID
    if (!decoded.id) {
      return next(new AppError('令牌格式错误', 401, -10086))
    }

    try {
      // 从数据库验证token
      const user = await User.findOne({
        where: {
          id: parseInt(decoded.id, 10),
          token
        }
      })

      if (!user) {
        return next(new AppError('令牌已失效', 401, -10086))
      }

      // 验证通过，将用户信息添加到请求对象
      req.user = decoded
      next()
    } catch (dbError) {
      // 数据库查询错误
      console.error('数据库查询失败:', dbError)
      return next(new AppError('验证令牌时发生错误', 500, -1))
    }
  } catch (jwtError) {
    // JWT验证失败
    if (jwtError instanceof jwt.JsonWebTokenError) {
      const message =
        jwtError.message === 'jwt expired'
          ? '令牌已过期'
          : jwtError.message === 'invalid signature'
            ? '无效的令牌签名'
            : jwtError.message === 'jwt malformed'
              ? '令牌格式错误'
              : '无效的认证令牌'
      return next(new AppError(message, 401, -10086))
    }

    // 其他未知错误
    console.error('验证token时发生未知错误:', jwtError)
    return next(new AppError('验证令牌时发生错误', 500, -1))
  }
}

/**
 * 检查用户是否为管理员的中间件
 */
export const isAdmin: AuthMiddleware = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): void => {
  try {
    if (!req.user) {
      return next(new AppError('未认证', 401, -10086))
    }

    if (req.user.role !== 'admin') {
      return next(new AppError('需要管理员权限', 403, -1))
    }

    next()
  } catch (error) {
    console.error('权限检查失败:', error)
    next(new AppError('权限检查失败', 500, -1))
  }
}

import { Response, NextFunction, Request } from 'express'
import { Import, Device } from '../models'
import { AppError } from '../middleware/error'
import { Op } from 'sequelize'
import { uploadImportContentToOSS } from '../utils/oss'
import { getDeviceTypeName, formatDateTime } from '../utils/utils'

// 定义查询参数接口
interface ImportQueryParams {
  file_name?: string
  page?: string
  pageSize?: string
  start_date?: string
  end_date?: string
}

/**
 * 获取导入数据列表
 */
export const getImportList = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const {
      file_name,
      page = '1',
      pageSize = '10',
      start_date,
      end_date
    } = req.query as ImportQueryParams

    // 建立查询条件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {}

    // 根据文件名称过滤
    if (file_name) {
      where.file_name = { [Op.like]: `%${file_name}%` }
    }

    // 添加时间段过滤
    if (start_date || end_date) {
      where.created_at = {}

      if (start_date) {
        where.created_at[Op.gte] = new Date(start_date)
      }

      if (end_date) {
        // 设置结束日期为当天的23:59:59
        const endDateTime = new Date(end_date)
        endDateTime.setHours(23, 59, 59, 999)
        where.created_at[Op.lte] = endDateTime
      }
    }

    // 分页设置
    const pageNumber = parseInt(page)
    const limit = parseInt(pageSize)
    const offset = (pageNumber - 1) * limit

    // 查询导入数据列表
    const { count, rows } = await Import.findAndCountAll({
      where,
      limit,
      offset,
      order: [['created_at', 'DESC']]
    })

    // 转换为前端需要的格式
    const imports = rows.map(importItem => {
      return {
        id: importItem.id,
        fileName: importItem.file_name,
        ossPath: importItem.oss_path,
        importRows: importItem.import_rows,
        createdAt: formatDateTime(importItem.created_at)
      }
    })

    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: imports
      }
    })
  } catch (error) {
    console.error('获取导入数据列表失败:', error)
    next(error)
  }
}

/**
 * 获取导入数据详情
 */
export const getImportDetail = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params

    const importItem = await Import.findByPk(id)

    if (!importItem) {
      throw new AppError('导入数据不存在', 404)
    }

    res.status(200).json({
      success: true,
      data: {
        id: importItem.id,
        fileName: importItem.file_name,
        ossPath: importItem.oss_path,
        content: importItem.content,
        importRows: importItem.import_rows,
        createdAt: formatDateTime(importItem.created_at)
      }
    })
  } catch (error) {
    console.error('获取导入数据详情失败:', error)
    next(error)
  }
}

/**
 * 解析CSV内容并创建设备记录
 * @param content CSV内容
 * @param importId 导入ID
 * @returns 成功创建的设备数量
 */
const processImportContent = async (content: string, importId: number): Promise<number> => {
  try {
    // 验证CSV内容格式
    const validationResult = validateCsvContent(content)
    if (!validationResult.isValid) {
      console.error(`CSV文件格式不正确: ${validationResult.message}`)
      return 0
    }

    // 将内容按行分割
    const lines = content.split('\n').filter(line => line.trim() !== '')

    // 跳过header行
    if (lines.length <= 1) {
      console.warn('CSV文件只有标题行或为空')
      return 0
    }

    // 解析header以确定各字段位置
    const headerCols = lines[0].split(',').map(col => col.trim().toLowerCase())
    const pkIndex = headerCols.findIndex(col => col === 'productkey')
    const snIndex = headerCols.findIndex(col => col === 'sn')
    const secretIndex = headerCols.findIndex(col => col === 'secret')
    const macIndex = headerCols.findIndex(col => col === 'mac')
    const deviceNameIndex = headerCols.findIndex(col => col === '设备名称')
    const projectNameIndex = headerCols.findIndex(col => col === '项目名称')

    let createdCount = 0

    // 处理数据行（跳过header）
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim()
      if (!line) continue

      const cols = line.split(',').map(col => col.trim())

      // 确保数据行有足够的列
      if (
        cols.length <=
        Math.max(pkIndex, snIndex, secretIndex, macIndex, deviceNameIndex, projectNameIndex)
      ) {
        console.warn(`第${i + 1}行数据列数不足，已跳过`)
        continue
      }

      // 提取设备数据
      const productKey = cols[pkIndex]
      const deviceSn = cols[snIndex]
      const deviceSecret = cols[secretIndex]
      const deviceMac = cols[macIndex]
      const deviceName = cols[deviceNameIndex]
      const projectName = cols[projectNameIndex]
      // 创建设备记录
      try {
        await Device.create({
          product_key: productKey,
          device_name: deviceName,
          device_sn: deviceSn,
          device_secret: deviceSecret,
          device_mac: deviceMac,
          project_name: projectName,
          creation_method: 1, // 生产导入
          import_id: importId,
          created_at: new Date(),
          modified_at: new Date()
        })
        createdCount++
      } catch (err: any) {
        console.error(`创建设备记录失败: ${err.message || '未知错误'}`)
      }
    }

    return createdCount
  } catch (error) {
    console.error('处理导入内容失败:', error)
    return 0
  }
}

/**
 * 验证CSV内容是否包含所有必要的列
 * @param content CSV内容
 * @returns 验证结果对象
 */
interface ValidationResult {
  isValid: boolean
  message?: string
}

const validateCsvContent = (content: string): ValidationResult => {
  // 确保内容不为空
  if (!content || content.trim() === '') {
    return { isValid: false, message: '文件内容为空' }
  }

  // 将内容按行分割
  const lines = content.split('\n').filter(line => line.trim() !== '')

  // 确保至少有标题行
  if (lines.length === 0) {
    return { isValid: false, message: '文件内容为空' }
  }

  // 检查标题行是否包含必要的列
  const header = lines[0].toLowerCase()
  const requiredColumns = ['productkey', 'sn', 'secret', 'mac', '设备名称', '项目名称']
  const missingColumns: string[] = []

  for (const column of requiredColumns) {
    if (!header.includes(column)) {
      missingColumns.push(column)
    }
  }

  if (missingColumns.length > 0) {
    return {
      isValid: false,
      message: `缺少必要的列: ${missingColumns.join(', ')}`
    }
  }

  return { isValid: true }
}

/**
 * 创建导入数据
 */
export const createImport = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { file_name, content } = req.body

    // 验证必填字段
    if (!file_name) {
      throw new AppError('文件名称不能为空', 400)
    }

    // 验证CSV内容格式
    if (content) {
      // 检查CSV是否包含必要的列
      const validationResult = validateCsvContent(content)
      if (!validationResult.isValid) {
        throw new AppError(`CSV文件格式不正确: ${validationResult.message}`, 400)
      }
    }

    // 如果有内容，上传到OSS
    let ossPath = undefined
    if (content) {
      try {
        // 上传内容到OSS
        ossPath = await uploadImportContentToOSS(content, file_name)
      } catch (ossError) {
        console.error('上传到OSS失败:', ossError)
        // 上传失败不影响后续操作，但记录错误
      }
    }

    // 创建导入记录
    const importItem = await Import.create({
      file_name,
      content,
      oss_path: ossPath,
      created_at: new Date()
    })

    // 解析CSV内容并创建设备记录
    let createdDevices = 0
    if (content) {
      createdDevices = await processImportContent(content, importItem.id)
    }

    // 更新导入记录的行数
    await importItem.update({
      import_rows: createdDevices
    })

    res.status(201).json({
      success: true,
      data: {
        ...importItem.get(),
        createdDevices
      }
    })
  } catch (error) {
    console.error('创建导入数据失败:', error)
    next(error)
  }
}

/**
 * 更新导入数据
 */
export const updateImport = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { file_name, content, import_rows } = req.body

    const importItem = await Import.findByPk(id)

    if (!importItem) {
      throw new AppError('导入数据不存在', 404)
    }

    // 准备更新的数据
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updateData: any = {
      file_name: file_name || importItem.file_name
    }

    // 更新行数
    if (import_rows !== undefined) {
      updateData.import_rows = import_rows
    }

    // 如果更新了内容，重新上传到OSS
    if (content !== undefined && content !== importItem.content) {
      updateData.content = content

      // 如果有内容，上传到OSS
      if (content) {
        try {
          // 上传内容到OSS
          const newFileName = file_name || importItem.file_name
          updateData.oss_path = await uploadImportContentToOSS(content, newFileName)
        } catch (ossError) {
          console.error('上传到OSS失败:', ossError)
        }
      } else {
        updateData.oss_path = undefined
      }
    }

    // 更新导入数据
    await importItem.update(updateData)

    res.status(200).json({
      success: true,
      data: importItem
    })
  } catch (error) {
    console.error('更新导入数据失败:', error)
    next(error)
  }
}

/**
 * 删除导入数据
 */
export const deleteImport = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params

    const importItem = await Import.findByPk(id)

    if (!importItem) {
      throw new AppError('导入数据不存在', 404)
    }

    await importItem.destroy()

    res.status(200).json({
      success: true,
      message: '导入数据删除成功'
    })
  } catch (error) {
    console.error('删除导入数据失败:', error)
    next(error)
  }
}

/**
 * 获取导入关联的设备列表
 */
export const getImportDevices = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { importId } = req.params
    const { page = '1', pageSize = '10' } = req.query

    // 验证导入数据是否存在
    const importItem = await Import.findByPk(importId)
    if (!importItem) {
      throw new AppError('导入数据不存在', 404)
    }

    // 分页设置
    const pageNumber = parseInt(page.toString())
    const limit = parseInt(pageSize.toString())
    const offset = (pageNumber - 1) * limit

    // 查询设备列表
    const { count, rows } = await Device.findAndCountAll({
      where: { import_id: importId },
      limit,
      offset,
      order: [['created_at', 'DESC']]
    })

    // 转换为前端需要的格式
    const devices = rows.map(device => {
      // 使用公共工具函数获取设备类型名称
      const typeName = getDeviceTypeName(device.product_key)
      return {
        id: device.id,
        type: device.product_key,
        typeName: typeName,
        deviceName: device.device_name,
        deviceSn: device.device_sn,
        deviceMac: device.device_mac,
        createdAt: formatDateTime(device.created_at),
        modifiedAt: formatDateTime(device.modified_at)
      }
    })

    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: devices
      }
    })
  } catch (error) {
    console.error('获取导入关联设备列表失败:', error)
    next(error)
  }
}

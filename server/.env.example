# 服务器配置
PORT=3001
NODE_ENV=development

# JWT配置 - 生产环境必须使用强密钥（至少64个字符）
JWT_SECRET=your_jwt_secret_key_should_be_at_least_64_characters_long_and_very_complex_with_random_characters_123456789

# PostgreSQL数据库配置
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_strong_database_password_with_special_chars_123!@#
DB_HOST=localhost
DB_PORT=5432

# 阿里云OSS配置
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_BUCKET=your_oss_bucket_name

# 安全配置
CORS_ORIGINS=http://localhost:5173,http://localhost:3000,https://yourdomain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=1000
LOGIN_RATE_LIMIT_MAX=5
SESSION_TIMEOUT=900000

# Cookie安全设置 - 当前生产环境使用HTTP，所以设置为false
COOKIE_SECURE=false
# Cookie SameSite策略 - 开发环境建议使用lax，生产环境使用strict
COOKIE_SAME_SITE=lax

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# 算法执行安全配置
ALGORITHM_MAX_EXECUTION_TIME=30000
ALGORITHM_MAX_MEMORY=134217728
ALGORITHM_MAX_CODE_SIZE=1048576

# 钉钉授权配置
DINGTALK_CLIENT_ID=
DINGTALK_CLIENT_SECRET=
DINGTALK_HOST=
DINGTALK_AUTH_TOKEN=
DINGTALK_USER_INFO=

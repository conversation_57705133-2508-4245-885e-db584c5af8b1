import axios, { AxiosError, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { useUserStore } from '@/store/user'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 15000,
  withCredentials: true // 允许携带Cookie
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 不再需要手动设置Authorization头，token通过HttpOnly Cookie自动发送
    return config
  },
  (error: AxiosError) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  (error: AxiosError) => {
    console.error('响应错误:', error)
    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response
      const errorData = data as { code?: number; message?: string }

      // 处理 token 无效的情况
      if (errorData.code === -10086) {
        // 使用 user store 的清除本地数据方法
        const userStore = useUserStore()
        userStore.clearLocalData()

        // 跳转到登录页
        window.location.href = '/login'

        return Promise.reject({
          status,
          message: errorData.message || '登录已过期，请重新登录',
          data: errorData
        })
      }

      return Promise.reject({
        status,
        message: errorData.message || '请求失败',
        data: errorData
      })
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      return Promise.reject({
        status: 0,
        message: '网络错误，请检查您的网络连接'
      })
    } else {
      // 请求配置出错
      return Promise.reject({
        status: 0,
        message: error.message || '请求配置错误'
      })
    }
  }
)

interface RequestConfig<T = unknown> extends InternalAxiosRequestConfig {
  data?: T
}

const request = <T = unknown, R = unknown>(config: RequestConfig<T>): Promise<R> => {
  return service(config)
}

export default request

<template>
  <el-card
    class="drilling-card sub-card rock-property-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        <span>钻进数据</span>
        <div class="header-controls">
          <el-select
            v-model="depthStep"
            placeholder="选择步长"
            size="small"
            style="width: 120px"
            clearable
            @change="handleDepthStepChange"
          >
            <el-option
              label="2cm"
              :value="2"
            />
            <el-option
              label="5cm"
              :value="5"
            />
            <el-option
              label="10cm"
              :value="10"
            />
          </el-select>
        </div>
      </div>
    </template>
    
    <div
      ref="tableWrapperRef"
      class="data-table-wrapper"
      :style="{ height: tableHeight + 'px' }"
    >
      <div class="data-table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th style="min-width: 150px">
                采集时间
              </th>
              <th style="min-width: 80px">
                工作模式
              </th>
              <th style="min-width: 70px">
                告警码
              </th>
              <th style="min-width: 80px">
                钻孔深度
              </th>
              <th style="min-width: 80px">
                旋转扭矩
              </th>
              <th style="min-width: 70px">
                推进力
              </th>
              <th style="min-width: 90px">
                高压水压力
              </th>
              <th style="min-width: 80px">
                旋转速度
              </th>
              <th style="min-width: 80px">
                钻进速度
              </th>
              <th style="min-width: 80px">
                液压压力
              </th>
              <th style="min-width: 90px">
                低压水压力
              </th>
              <th style="min-width: 100px">
                工作时长高位
              </th>
              <th style="min-width: 100px">
                工作时长低位
              </th>
              <th style="min-width: 80px">
                旋转压力
              </th>
              <th style="min-width: 80px">
                推进压力
              </th>
              <th style="min-width: 70px">
                标段
              </th>
              <th style="min-width: 70px">
                孔号
              </th>
              <th style="min-width: 80px">
                孔倾角
              </th>
              <th style="min-width: 60px">
                RC0
              </th>
              <th style="min-width: 60px">
                RC1
              </th>
              <th style="min-width: 60px">
                RC2
              </th>
              <th style="min-width: 60px">
                RC3
              </th>
              <th style="min-width: 60px">
                RC4
              </th>
              <th style="min-width: 60px">
                RC5
              </th>
              <th style="min-width: 60px">
                RC6
              </th>
              <th style="min-width: 120px">
                隧道名称
              </th>
              <th style="min-width: 80px">
                钻头直径
              </th>
              <th style="min-width: 80px">
                岩石强度
              </th>
              <th style="min-width: 100px">
                围岩等级
              </th>
              <th style="min-width: 100px">
                地质预警
              </th>
              <th style="min-width: 100px">
                设备预警
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="item in pagedDrillingData"
              :key="item.collectionAt"
            >
              <td>{{ item.collectionAt }}</td>
              <td>{{ item.mode }}</td>
              <td>{{ item.wrmCd }}</td>
              <td>{{ item.dpth }}</td>
              <td>{{ item.rtnTq }}</td>
              <td>{{ item.frcstKn }}</td>
              <td>{{ item.wtrPrsH }}</td>
              <td>{{ item.rtnSpd }}</td>
              <td>{{ item.advncSpd }}</td>
              <td>{{ item.hydrPrs }}</td>
              <td>{{ item.wtrPrsL }}</td>
              <td>{{ item.hghWrk }}</td>
              <td>{{ item.lwWrk }}</td>
              <td>{{ item.rtnPrs }}</td>
              <td>{{ item.frcstPrs }}</td>
              <td>{{ item.tunnelDpth }}</td>
              <td>{{ item.hlNum }}</td>
              <td>{{ item.hlAng }}</td>
              <td>{{ item.rc0 }}</td>
              <td>{{ item.rc1 }}</td>
              <td>{{ item.rc2 }}</td>
              <td>{{ item.rc3 }}</td>
              <td>{{ item.rc4 }}</td>
              <td>{{ item.rc5 }}</td>
              <td>{{ item.rc6 }}</td>
              <td>{{ item.tunnelName }}</td>
              <td>{{ item.diameter }}</td>
              <td>{{ item.rockStrengthDesc }}</td>
              <td>{{ item.rockGradeDesc }}</td>
              <td>{{ item.geologicalWarning }}</td>
              <td>{{ item.equipmentWarning }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <div class="pagination-container">
      <el-pagination
        style="margin-top: 0;"
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[100, 500, 1000]"
        layout="total, sizes, prev, pager, next"
        :total="totalItems"
        size="small"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script>
import { computed, ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { debouncedFilter } from '@/utils/debounce'

export default {
  name: 'DrillingDataTableCard',
  props: {
    drillingData: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    // DOM引用
    const tableWrapperRef = ref(null)

    // 内部状态：分页控制
    const currentPage = ref(1)
    const pageSize = ref(1000) // 默认每页显示1000条

    // 步长过滤状态
    const depthStep = ref(null) // 选择的步长，默认为null（不过滤）

    // 高度计算相关状态
    const tableHeight = ref(539) // 默认表格高度
    let lastCalculatedHeight = 0 // 记录上次计算的高度，避免无限循环
    let resizeTimer = null // 防抖定时器
    
    // 根据步长过滤数据
    const filteredDrillingData = computed(() => {
      if (!depthStep.value || !props.drillingData.length) {
        return props.drillingData
      }

      // 按钻进深度进行步长过滤和去重
      const step = depthStep.value // 步长，单位cm
      const depthMap = new Map() // 用于存储每个深度区间的数据

      // 对数据按深度排序（假设dpth字段是数值类型）
      const sortedData = [...props.drillingData].sort((a, b) => {
        const depthA = parseFloat(a.dpth) || 0
        const depthB = parseFloat(b.dpth) || 0
        return depthA - depthB
      })

      for (const item of sortedData) {
        const currentDepth = parseFloat(item.dpth) || 0

        // 计算当前深度所属的步长区间
        const depthInterval = Math.floor(currentDepth / step) * step

        // 如果这个深度区间还没有数据，或者当前数据的时间更新，则使用当前数据
        if (!depthMap.has(depthInterval)) {
          depthMap.set(depthInterval, item)
        } else {
          // 如果已有数据，比较时间戳，保留最新的数据
          const existingItem = depthMap.get(depthInterval)
          const currentTime = new Date(item.collectionAt).getTime()
          const existingTime = new Date(existingItem.collectionAt).getTime()

          if (currentTime > existingTime) {
            depthMap.set(depthInterval, item)
          }
        }
      }

      // 将Map中的数据转换为数组，并按深度排序
      const result = Array.from(depthMap.values()).sort((a, b) => {
        const depthA = parseFloat(a.dpth) || 0
        const depthB = parseFloat(b.dpth) || 0
        return depthA - depthB
      })

      return result
    })

    // 计算总条数（基于过滤后的数据）
    const totalItems = computed(() => filteredDrillingData.value.length)

    // 计算当前页数据（基于过滤后的数据）
    const pagedDrillingData = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredDrillingData.value.slice(start, end)
    })

    // 处理分页大小变化
    const handleSizeChange = (val) => {
      pageSize.value = val
      currentPage.value = 1 // 页大小改变时，通常返回第一页
    }

    // 处理页码变化
    const handleCurrentChange = (val) => {
      currentPage.value = val
    }

    // 创建防抖的步长变化处理函数
    const debouncedDepthStepChange = debouncedFilter(() => {
      // 步长变化时重置到第一页
      currentPage.value = 1
    })

    // 处理步长变化 - 使用防抖优化
    const handleDepthStepChange = debouncedDepthStepChange
    
    // 计算表格容器的最佳高度 - 参考其他dashboard组件的实现
    // 实现动态高度计算：
    // 1. 当父组件高度未设置时，table高度为539px，分页高度为60px
    // 2. 当父组件高度设置了，table高度 = 父组件高度 - header - 分页(60px) - padding
    const calculateTableHeight = () => {
      if (!tableWrapperRef.value) {
        return 539 // 默认表格高度
      }

      // 获取父容器的实际高度 - 使用 el-card 作为参考容器
      const cardBodyElement = tableWrapperRef.value.parentElement // el-card__body
      const cardElement = cardBodyElement?.parentElement // el-card

      // 使用 el-card 作为参考容器（第二层）
      const referenceElement = cardElement

      // 1. 当父级高度没有设置时，使用默认高度
      if (!referenceElement || referenceElement.offsetHeight <= 100) {
        return 539 // 父级高度没有设置时的默认表格高度
      }

      // 2. 当父级高度设置了时，按照父级高度自动调整，不使用默认高度
      const parentHeight = referenceElement.offsetHeight

      // 3. 获取实际的header高度
      const headerElement = cardElement?.querySelector('.el-card__header')
      const actualHeaderHeight = headerElement ? headerElement.offsetHeight : 80

      // 4. 获取实际的padding（el-card__body的padding）
      const cardBodyStyles = window.getComputedStyle(cardBodyElement)
      const paddingTop = parseInt(cardBodyStyles.paddingTop) || 0
      const paddingBottom = parseInt(cardBodyStyles.paddingBottom) || 0
      const actualPadding = paddingTop + paddingBottom

      // 5. 分页组件高度固定为60px
      const paginationHeight = 60

      // 计算表格区域可用高度：父级容器高度 - header - 分页 - padding
      const availableHeight = parentHeight - actualHeaderHeight - paginationHeight - actualPadding

      // 如果计算出的高度太小（可能是DOM还没完全渲染），使用默认高度
      if (availableHeight < 200) {
        return 539 // 使用默认高度，而不是强制使用计算出的小值
      }

      return availableHeight
    }

    // 更新表格高度
    const updateTableHeight = () => {
      if (tableWrapperRef.value) {
        const newHeight = calculateTableHeight()

        // 只有当高度变化超过10px时才更新，避免无限循环
        if (Math.abs(newHeight - lastCalculatedHeight) > 10) {
          lastCalculatedHeight = newHeight
          tableHeight.value = newHeight
        }
      }
    }

    // 处理窗口大小变化
    const handleResize = () => {
      // 清除之前的定时器，实现防抖
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }

      resizeTimer = setTimeout(() => {
        updateTableHeight()
      }, 150) // 150ms防抖延迟
    }

    // 当数据源或步长变化时，重置到第一页
    watch(() => props.drillingData, () => {
      currentPage.value = 1
    })

    watch(() => depthStep.value, () => {
      currentPage.value = 1
    })

    // 组件挂载后初始化高度计算
    onMounted(() => {
      nextTick(() => {
        updateTableHeight()
        // 添加窗口大小变化监听
        window.addEventListener('resize', handleResize)
      })
    })

    // 组件销毁前清理事件监听器
    onBeforeUnmount(() => {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', handleResize)

      // 清理定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }
    })

    return {
      tableWrapperRef,
      tableHeight,
      currentPage,
      pageSize,
      depthStep,
      totalItems,
      pagedDrillingData,
      handleSizeChange,
      handleCurrentChange,
      handleDepthStepChange
    }
  }
}
</script>

<style scoped>
.drilling-card {
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
}

.drilling-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.drilling-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.sub-card-header span:first-child {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  position: relative;
}

.sub-card-header span:first-child::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.drilling-card :deep(.el-card__body) {
  padding: 0;
  overflow: hidden;
  max-width: 100%;
}

.data-table-wrapper {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.data-table-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow-x: auto;
  overflow-y: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  white-space: nowrap;
}

.data-table th,
.data-table td {
  padding: 14px 8px;
  text-align: center;
  font-size: 14px;
  border: 1px solid #EBEEF5;
  overflow: hidden;
  text-overflow: ellipsis;
}

.data-table th {
  background-color: #F5F7FA;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 2;
  box-shadow: 0 2px 5px -2px rgba(0,0,0,0.1);
}

.data-table tr:nth-child(even) {
  background-color: #FAFAFA;
}

.data-table tr:hover {
  background-color: #F5F7FA;
}

/* 固定第一列 */
.data-table th:first-child,
.data-table td:first-child {
  position: sticky;
  left: 0;
  z-index: 2;
}

.data-table th:first-child {
  background-color: #F5F7FA;
  box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1);
  z-index: 3; /* 增加z-index使其在同为sticky的其他表头之上 */
}

.data-table tr:nth-child(odd) td:first-child {
  background-color: #FFFFFF;
  box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1);
}

.data-table tr:nth-child(even) td:first-child {
  background-color: #FAFAFA;
  box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1);
}

.data-table tr:hover td:first-child {
  background-color: #F5F7FA;
}

.pagination-container {
  height: 60px;
  padding: 15px 20px;
  position: relative;
  z-index: 2;
  background: white;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: end;
}
</style> 
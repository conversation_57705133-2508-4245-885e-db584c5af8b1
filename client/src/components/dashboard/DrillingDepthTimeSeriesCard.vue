<template>
  <el-card
    class="drilling-depth-time-series-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        <span>钻进深度时序分析</span>
        <div class="chart-controls">
          <!-- 显示深度变化率开关 -->
          <div class="depth-rate-switch">
            <el-switch
              v-model="showDepthChangeRate"
              size="small"
              active-text="深度变化率"
              @change="handleDepthRateToggle"
            />
          </div>
        </div>
      </div>
    </template>

    <!-- 图表容器 -->
    <div
      ref="depthTimeSeriesChart"
      class="depth-time-series-chart"
    />

    <!-- 统计信息 -->
    <div class="statistics-panel">
      <div class="stat-item">
        <span class="stat-label">总数据点:</span>
        <span class="stat-value">{{ statistics.totalPoints }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">正常点:</span>
        <span class="stat-value normal">{{ statistics.normalPoints }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">卡钻点:</span>
        <span class="stat-value stuck">{{ statistics.stuckPoints }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">突进点:</span>
        <span class="stat-value mutation">{{ statistics.mutationPoints }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">深度范围:</span>
        <span class="stat-value">{{ statistics.depthRange }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">时间跨度:</span>
        <span class="stat-value">{{ statistics.timeSpan }}</span>
      </div>
    </div>
  </el-card>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'DrillingDepthTimeSeriesCard', // 明确定义emits，即使为空
  props: {
    // 钻进深度时序数据
    depthTimeSeriesData: {
      type: Array,
      default: () => []
    }
  },
  emits: [],
  setup(props) {
    const depthTimeSeriesChart = ref(null)
    let chartInstance = null

    // 图表配置
    const showDepthChangeRate = ref(false) // 是否显示深度变化率
    
    // 图表常量
    const CHART_CONSTANTS = {
      COLORS: {
        NORMAL: '#52c41a',
        STUCK: '#ff4d4f',
        MUTATION: '#fa8c16'
      },
      CHART_HEIGHT: 450
    }

    // 处理后的数据
    const processedData = computed(() => {
      if (!Array.isArray(props.depthTimeSeriesData) || props.depthTimeSeriesData.length === 0) {
        return []
      }

      return [...props.depthTimeSeriesData]
    })

    // 统计信息
    const statistics = computed(() => {
      const data = props.depthTimeSeriesData || []
      const totalPoints = data.length
      const stuckPoints = data.filter(item => item.isStuckPoint).length
      const mutationPoints = data.filter(item => item.isMutationPoint).length
      const normalPoints = totalPoints - stuckPoints - mutationPoints

      // 计算深度范围
      const depths = data.map(item => item.depth).filter(d => !isNaN(d))
      const minDepth = depths.length > 0 ? Math.min(...depths) : 0
      const maxDepth = depths.length > 0 ? Math.max(...depths) : 0
      const depthRange = depths.length > 0 ? `${minDepth.toFixed(1)} - ${maxDepth.toFixed(1)} cm` : '无数据'

      // 计算时间跨度
      const times = data.map(item => new Date(item.collectionAt).getTime()).filter(t => !isNaN(t))
      const timeSpan = times.length > 1 
        ? `${((Math.max(...times) - Math.min(...times)) / 1000 / 60).toFixed(1)} 分钟`
        : '无数据'

      return {
        totalPoints,
        normalPoints,
        stuckPoints,
        mutationPoints,
        depthRange,
        timeSpan
      }
    })

    // 创建图表配置
    const createChartOption = () => {
      const data = processedData.value

      if (data.length === 0) {
        return {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'middle',
            textStyle: {
              color: '#999',
              fontSize: 16
            }
          }
        }
      }

      // 准备数据系列
      const normalData = []
      const stuckData = []
      const mutationData = []
      const depthChangeRateData = [] // 深度变化率数据

      data.forEach(item => {
        const point = [
          new Date(item.collectionAt).getTime(),
          item.depth,
          item // 存储完整数据用于tooltip
        ]

        // 深度变化率数据点
        const ratePoint = [
          new Date(item.collectionAt).getTime(),
          item.depthChangeRate,
          item
        ]
        depthChangeRateData.push(ratePoint)

        if (item.isStuckPoint) {
          stuckData.push(point)
        } else if (item.isMutationPoint) {
          mutationData.push(point)
        } else {
          normalData.push(point)
        }
      })

      const series = []

      // 正常数据系列 - 固定为折线图
      if (normalData.length > 0) {
        series.push({
          name: '正常钻进',
          type: 'line',
          data: normalData,
          itemStyle: {
            color: CHART_CONSTANTS.COLORS.NORMAL
          },
          lineStyle: {
            color: CHART_CONSTANTS.COLORS.NORMAL,
            width: 2
          },
          showSymbol: false,
          symbolSize: 4
        })
      }

      // 卡钻数据系列 - 只显示点，不连线
      if (stuckData.length > 0) {
        series.push({
          name: '卡钻状态',
          type: 'scatter',
          data: stuckData,
          itemStyle: {
            color: CHART_CONSTANTS.COLORS.STUCK
          },
          symbolSize: 10,
          symbol: 'circle'
        })
      }

      // 突进数据系列 - 只显示点，不连线
      if (mutationData.length > 0) {
        series.push({
          name: '突进状态',
          type: 'scatter',
          data: mutationData,
          itemStyle: {
            color: CHART_CONSTANTS.COLORS.MUTATION
          },
          symbolSize: 10,
          symbol: 'triangle'
        })
      }

      // 添加深度变化率系列（如果开关开启）
      if (showDepthChangeRate.value && depthChangeRateData.length > 0) {
        series.push({
          name: '深度变化率',
          type: 'line',
          yAxisIndex: 1, // 使用第二个Y轴
          data: depthChangeRateData,
          itemStyle: {
            color: '#722ed1'
          },
          lineStyle: {
            color: '#722ed1',
            width: 2,
            type: 'dashed'
          },
          showSymbol: false,
          symbolSize: 4
        })
      }

      return {
        tooltip: {
          trigger: 'axis', // 改为axis触发，鼠标移动到曲线上就显示
          axisPointer: {
            type: 'cross', // 显示十字准线
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: function(params) {
            if (!params || params.length === 0) return ''

            // 获取时间轴的值
            const timeValue = params[0].axisValue
            const time = new Date(timeValue).toLocaleString()

            let tooltipContent = `<div style="padding: 8px;"><div><strong>采集时间:</strong> ${time}</div>`

            // 遍历所有系列的数据
            params.forEach(param => {
              const data = param.data[2] // 获取完整数据
              if (!data) return

              const seriesName = param.seriesName
              const color = param.color

              if (seriesName === '深度变化率') {
                tooltipContent += `<div><strong style="color: ${color};">${seriesName}:</strong> ${data.depthChangeRate} cm/s</div>`
              } else {
                const status = data.isStuckPoint ? '卡钻' : (data.isMutationPoint ? '突进' : '正常')
                tooltipContent += `<div><strong style="color: ${color};">${seriesName}:</strong></div>`
                tooltipContent += `<div style="margin-left: 10px;">深度: ${data.depth} cm</div>`
                tooltipContent += `<div style="margin-left: 10px;">状态: ${status}</div>`
                tooltipContent += `<div style="margin-left: 10px;">钻进速度: ${data.advncSpd} cm/min</div>`
                tooltipContent += `<div style="margin-left: 10px;">旋转速度: ${data.rtnSpd} rpm</div>`
                tooltipContent += `<div style="margin-left: 10px;">扭矩: ${data.rtnTq} Nm</div>`
                tooltipContent += `<div style="margin-left: 10px;">推进力: ${data.frcstKn} kN</div>`
              }
            })

            tooltipContent += '</div>'
            return tooltipContent
          }
        },
        legend: {
          data: series.map(s => s.name),
          top: -5,
          left: 'center'
        },
        grid: {
          left: 65,
          right: 65,
          bottom: 80, // 增加底部空间，为X轴标题留出更多空间
          top: 30,
          containLabel: true
        },
        xAxis: {
          type: 'time',
          name: '采集时间',
          nameLocation: 'end', // 改为中间位置
          nameGap: 0, // 控制标题与轴线的距离
          nameTextStyle: {
            align: 'center',
            verticalAlign: 'top',
            padding: [8, 0, 0, 0] // 向下偏移15像素：[上, 右, 下, 左]
          },
          axisLabel: {
            formatter: function(value) {
              return echarts.format.formatTime('MM-dd hh:mm', value) // 移除换行符
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '钻进深度 (cm)',
            nameLocation: 'middle',
            nameGap: 50,
            position: 'left',
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: showDepthChangeRate.value ? '深度变化率 (cm/s)' : '', // 根据开关状态显示标题
            nameLocation: 'middle',
            nameGap: 50,
            position: 'right',
            show: showDepthChangeRate.value, // 根据开关状态显示/隐藏整个Y轴
            axisLabel: {
              formatter: '{value}'
            },
            splitLine: {
              show: false // 隐藏右侧Y轴的网格线，避免重复
            }
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: 0
          },
          {
            type: 'slider',
            xAxisIndex: 0,
            bottom: '5%'
          }
        ],
        toolbox: {
          feature: {
            dataView: { readOnly: false },
            saveAsImage: {}
          }
        },
        series: series
      }
    }

    // 初始化图表
    const initChart = () => {
      try {
        if (chartInstance) {
          chartInstance.dispose()
          chartInstance = null
        }

        if (!depthTimeSeriesChart.value) {
          return
        }

        // 确保DOM元素存在且已挂载
        if (!depthTimeSeriesChart.value.parentNode) {
          return
        }

        // 设置图表高度
        depthTimeSeriesChart.value.style.height = CHART_CONSTANTS.CHART_HEIGHT + 'px'

        chartInstance = echarts.init(depthTimeSeriesChart.value)
        const option = createChartOption()
        chartInstance.setOption(option)
      } catch (error) {
        console.error('图表初始化失败:', error)
      }
    }

    // 事件处理
    const handleDepthRateToggle = () => {
      nextTick(() => {
        initChart()
      })
    }

    // 监听数据变化
    watch(
      () => props.depthTimeSeriesData,
      (newData) => {
        if (newData && Array.isArray(newData)) {
          nextTick(() => {
            if (depthTimeSeriesChart.value) {
              initChart()
            }
          })
        }
      },
      { deep: true }
    )

    // 组件挂载和卸载
    onMounted(() => {
      nextTick(() => {
        setTimeout(() => {
          if (depthTimeSeriesChart.value) {
            initChart()
          }
        }, 100)
      })
    })

    onUnmounted(() => {
      try {
        if (chartInstance) {
          chartInstance.dispose()
          chartInstance = null
        }
      } catch (error) {
        console.error('图表销毁失败:', error)
      }
    })

    return {
      depthTimeSeriesChart,
      showDepthChangeRate,
      statistics,
      handleDepthRateToggle
    }
  }
}
</script>

<style scoped>
.drilling-depth-time-series-card {
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
}

.drilling-depth-time-series-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #52c41a, #fa8c16);
  z-index: 1;
}

.drilling-depth-time-series-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.sub-card-header span:first-child {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  position: relative;
}

.sub-card-header span:first-child::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #52c41a;
  border-radius: 3px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.depth-rate-switch {
  display: flex;
  align-items: center;
}

.depth-time-series-chart {
  width: 100%;
}

.statistics-panel {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.stat-value.normal {
  color: #52c41a;
}

.stat-value.stuck {
  color: #ff4d4f;
}

.stat-value.mutation {
  color: #fa8c16;
}
</style>

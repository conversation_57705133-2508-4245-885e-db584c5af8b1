import express, { Request, Response, NextFunction } from 'express'
import cors from 'cors'
import morgan from 'morgan'
import compression from 'compression'
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'
import cookieParser from 'cookie-parser'
import config from './config'
import { errorHandler } from './middleware/error'
import { requestLogger } from './middleware/logger'
import router from './routes'
// 导入数据库相关
import { testConnection } from './config/database'
import { syncDatabase } from './models/sequelize'
import { AppError } from './middleware/error'
import zlib from 'zlib'

const app = express()

// 初始化数据库连接（异步操作，但不阻塞应用启动）
const initializeDatabase = async () => {
  try {
    console.log('🔄 正在初始化数据库连接...')
    await testConnection()
    console.log('✅ 数据库连接测试成功')

    console.log('🔄 正在同步数据库模型...')
    await syncDatabase(false) // 设置为false确保不会删除现有表
    console.log('✅ 数据库模型同步完成')
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error)
    // 不要立即退出，让应用继续运行，但记录错误
    // 这样可以让应用在数据库临时不可用时仍能启动
    console.warn('⚠️ 应用将继续运行，但数据库功能可能不可用')
  }
}

// 异步初始化数据库，不阻塞应用启动
initializeDatabase()

// 启用响应压缩 - 放在最前面以确保所有响应都被压缩
app.use(compression({
  // 使用zlib的最高压缩级别
  level: zlib.constants.Z_BEST_COMPRESSION,
  // 强制启用压缩，即使对较小响应
  threshold: 0,
  // 压缩所有内容类型
  filter: () => true,
  // 设置内存使用级别，提高性能
  memLevel: 9,
  // 用于确定压缩等级的窗口大小
  windowBits: 15,
  // 强制压缩类型
  strategy: zlib.constants.Z_DEFAULT_STRATEGY,
  // 禁用缓冲，直接压缩
  chunkSize: 16384
}));

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // 避免与某些第三方服务冲突
}))

// API请求频率限制（使用配置文件中的值）
const apiLimiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs,
  max: config.security.rateLimitMax,
  message: {
    status: 'error',
    message: '请求过于频繁，请稍后再试',
    code: -1
  },
  standardHeaders: true,
  legacyHeaders: false,
})

// 登录接口特殊限制（使用配置文件中的值）
const loginLimiter = rateLimit({
  windowMs: config.security.rateLimitWindowMs,
  max: config.security.loginRateLimitMax,
  message: {
    status: 'error',
    message: '登录尝试过于频繁，请稍后再试',
    code: -1
  },
  skipSuccessfulRequests: true, // 成功的请求不计入限制
})

app.use('/api', apiLimiter)
app.use('/api/users/login', loginLimiter)

// 请求信息记录中间件（仅开发环境）
if (config.nodeEnv === 'development') {
  app.use((req: Request, res: Response, next: NextFunction) => {
    console.log('📝 请求信息:', {
      method: req.method,
      url: req.url,
      origin: req.get('Origin') || 'undefined',
      referer: req.get('Referer') || 'undefined',
      userAgent: req.get('User-Agent')?.substring(0, 50) + '...',
      host: req.get('Host'),
      timestamp: new Date().toISOString()
    })
    next()
  })
}

// 强制所有响应类型为JSON
app.use((req: Request, res: Response, next: NextFunction) => {
  res.setHeader('Content-Type', 'application/json')
  next()
})

// 配置CORS策略（适配同源和跨域部署）
app.use(cors({
  origin: function (origin, callback) {
    try {
      // 从配置文件获取允许的域名列表
      const allowedOrigins = config.security.corsOrigins || []

      // 详细的CORS日志
      console.log('CORS检查:', {
        requestOrigin: origin || 'undefined (同源请求)',
        allowedOrigins: allowedOrigins,
        nodeEnv: config.nodeEnv,
        timestamp: new Date().toISOString()
      })

      // 处理无Origin的情况（同源请求、直接访问、Postman等）
      if (!origin) {
        // 同源请求是安全的，无论开发环境还是生产环境都允许
        console.log('✅ 允许无Origin请求（同源/直接访问/Postman）')
        return callback(null, true)
      }

      // 检查跨域请求的origin是否在允许列表中
      if (allowedOrigins.indexOf(origin) !== -1) {
        console.log('✅ CORS允许跨域请求:', origin)
        callback(null, true)
      } else {
        console.warn('❌ CORS拒绝跨域请求:', origin, '不在允许列表中:', allowedOrigins)

        // 开发环境可以临时允许（便于调试）
        if (config.nodeEnv === 'development') {
          console.warn('⚠️ 开发环境临时允许CORS请求，生产环境请配置正确的域名')
          callback(null, true)
        } else {
          // 生产环境严格拒绝未授权的跨域请求
          callback(new Error(`不允许的CORS请求来源: ${origin}`))
        }
      }
    } catch (error) {
      console.error('CORS配置错误:', error)
      // 发生错误时允许请求，避免服务中断
      callback(null, true)
    }
  },
  credentials: true, // 允许携带凭证
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Cache-Control'
  ],
  exposedHeaders: ['X-Total-Count'], // 暴露给前端的响应头
  maxAge: 86400 // 预检请求缓存时间（24小时）
}))
// 合理的请求体大小限制（减少安全风险）
app.use(express.json({
  limit: '10mb', // 从100mb减少到10mb
  verify: (req, res, buf) => {
    // 验证JSON格式，防止恶意payload
    try {
      JSON.parse(buf.toString())
    } catch (e) {
      throw new Error('无效的JSON格式')
    }
  }
}))
app.use(express.urlencoded({
  limit: '10mb',
  extended: true,
  parameterLimit: 1000 // 限制参数数量
}))
// Cookie解析中间件
app.use(cookieParser())
app.use(morgan('dev'))
app.use(requestLogger)

// 路由
app.use(router)

// 处理404错误
app.use((req: Request, res: Response, next: NextFunction) => {
  const err = new AppError(`找不到路径: ${req.originalUrl}`, 404, -1)
  next(err)
})

// 未捕获的错误转换器 - 将所有错误转为JSON格式
app.use((err: any, req: Request, res: Response, next: NextFunction) => {
  // 如果还没有状态码，设置为500
  if (!err.statusCode) err.statusCode = 500

  // 如果是令牌相关错误，设置相应的code
  if (
    err.message &&
    (err.message.includes('令牌') ||
      err.message.includes('认证') ||
      err.message.includes('token') ||
      err.message.includes('Token'))
  ) {
    return res.status(err.statusCode || 401).json({
      status: 'fail',
      message: err.message || '认证失败',
      code: -10086
    })
  }

  // 继续传递给errorHandler
  next(err)
})

// 错误处理
app.use(errorHandler)

export default app

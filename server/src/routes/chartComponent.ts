import { Router } from 'express'
import {
  getAllCharts,
  getChartById,
  createChart,
  update<PERSON>hart,
  deleteChart,
  getChartsByType,
  getChartsByProduct,
  setChartStatus,
  getChartStats
} from '../controllers/chartComponent'
import { asyncHandler } from '../utils/asyncHandler'
import { verifyToken } from '../middleware/auth'

const router = Router()

// 获取图表组件列表
router.get('/', asyncHandler(getAllCharts))

// 获取图表组件统计信息
router.get('/stats', asyncHandler(getChartStats))

// 根据图表类型获取图表组件
router.get('/type', asyncHandler(getChartsByType))

// 根据产品类型获取图表组件
router.get('/product', asyncHandler(getChartsByProduct))

// 获取图表组件详情
router.get('/:id', asyncHandler(getChartById))

// 创建图表组件
router.post('/', asyncHandler(verifyToken), asyncHandler(createChart))

// 更新图表组件
router.put('/:id', asyncHandler(verifyToken), asyncHandler(updateChart))

// 删除图表组件
router.delete('/:id', asyncHandler(verifyToken), asyncHandler(deleteChart))

// 启用/禁用图表组件
router.patch('/:id/status', asyncHandler(verifyToken), asyncHandler(setChartStatus))

export default router 
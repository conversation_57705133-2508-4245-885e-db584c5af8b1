import request from '@/utils/request'
import type { Device } from '@/types/api'
import type { AxiosRequestHeaders } from 'axios'

/**
 * 获取设备列表
 */
export const getDeviceList = (params: {
  page?: number
  pageSize?: number
  type?: string
  name?: string
  serialNumber?: string
}): Promise<{
  success: boolean
  data: {
    total: number
    list: Device[]
  }
}> => {
  return request({
    url: '/devices',
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备详情
 */
export const getDeviceDetail = (
  id: string
): Promise<{
  success: boolean
  data: Device
}> => {
  return request({
    url: `/devices/${id}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备关联的算法
 */
export function getDeviceArithmetic(deviceId: string) {
  return request({
    url: `/device-arithmetics/${deviceId}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 设置设备关联算法
 */
export function setDeviceArithmetic(deviceId: string, data: any) {
  return request({
    url: `/device-arithmetics/${deviceId}`,
    method: 'post',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 删除设备关联算法
 */
export function deleteDeviceArithmetic(deviceId: string, arithmeticType: number) {
  return request({
    url: `/device-arithmetics/${deviceId}/${arithmeticType}`,
    method: 'delete',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 批量设置设备关联算法
 */
export function batchSetDeviceArithmetic(data: {
  deviceIds: number[]
  arithmeticId: number
  arithmeticType: number
}) {
  return request({
    url: `/device-arithmetics/batch`,
    method: 'post',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备算法设置历史记录
 */
export function getDeviceArithmeticRecords(
  deviceId: string,
  params?: { startDate?: string; endDate?: string; page?: number; pageSize?: number }
) {
  return request({
    url: `/device-arithmetics/records/${deviceId}`,
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备列表（包含算法信息）
 */
export function getDeviceListWithAlgorithm(params: {
  page?: number
  pageSize?: number
  type?: string
  name?: string
  serialNumber?: string
  algorithmType?: number
  arithmeticId?: number
}): Promise<{
  success: boolean
  data: {
    total: number
    list: Device[]
  }
}> {
  return request({
    url: '/devices/with-algorithm',
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 更新设备信息
 */
export const updateDevice = (
  id: string,
  data: {
    deviceName?: string
    serialNumber?: string
    macAddress?: string
    deviceSecret?: string
  }
): Promise<{
  success: boolean
  data: Device
  message?: string
}> => {
  return request({
    url: `/devices/${id}`,
    method: 'put',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备数字岩芯数据
 */
export const getDeviceDigitalCore = (
  deviceId: string,
  params: {
    page?: number
    pageSize?: number
  } = {}
): Promise<{
  success: boolean
  data: {
    total: number
    list: Array<{
      fileId: number
      fileName: string
      imageName: string
      fileSize: number
      holeNo: string
      startTime: string
      endTime: string
      createdAt: string
      dataCount: number
      startDepth: number | null
      endDepth: number | null
    }>
  }
}> => {
  return request({
    url: `/devices/${deviceId}/digital-core`,
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备数字岩芯详情
 */
export const getDeviceDigitalCoreDetail = (
  deviceId: string,
  fileId: string
): Promise<{
  success: boolean
  data: {
    fileId: number
    fileName: string
    imageName: string
    fileSize: number
    holeNo: string
    startTime: string
    endTime: string
    createdAt: string
    dataCount: number
    startDepth: number | null
    endDepth: number | null
  }
}> => {
  return request({
    url: `/devices/${deviceId}/digital-core/${fileId}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

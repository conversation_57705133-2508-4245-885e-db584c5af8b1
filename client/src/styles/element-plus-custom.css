/* Element Plus 自定义样式 */
:root {
  --el-color-primary: var(--apple-primary);
  --el-color-success: var(--apple-success);
  --el-color-warning: var(--apple-warning);
  --el-color-danger: var(--apple-error);
  --el-border-radius-base: 8px;
}

/* 卡片样式 */
.el-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: var(--apple-transition);
}

.el-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--apple-border);
}

.el-card__body {
  padding: 20px;
}

/* 按钮样式 */
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}

.el-button--primary {
  background-color: var(--apple-primary);
}

.el-button--primary:hover {
  background-color: #0062c3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 113, 227, 0.2);
}

/* 表格样式 */
.el-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.el-table th.el-table__cell {
  background-color: #f8f8fa;
  font-weight: 600;
  color: var(--apple-text-primary);
}

.el-table .el-table__cell {
  padding: 12px 0;
}

/* 表格中的按钮悬停样式 */
.el-table .el-button:hover {
  border: none;
  box-shadow: none;
}

/* 分页样式 */
.el-pagination {
  margin-top: 20px;
  justify-content: flex-end;
}

.el-pagination .el-pagination__jump,
.el-pagination .el-input__inner,
.el-pagination button {
  height: 32px;
  font-size: 14px;
}

/* 表单样式 */
.el-form-item__label {
  font-weight: 500;
}

.el-input__inner {
  border-radius: 8px;
}

.el-input__inner:focus {
  border-color: var(--apple-primary);
}

/* 对话框样式 */
.el-dialog {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.el-dialog__header {
  padding: 20px;
  border-bottom: 1px solid var(--apple-border);
}

.el-dialog__title {
  font-weight: 600;
  font-size: 18px;
}

.el-dialog__body {
  padding: 24px;
}

.el-dialog__footer {
  padding: 16px 20px;
  border-top: 1px solid var(--apple-border);
}

/* 消息提示样式 */
.apple-message {
  border-radius: 10px !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  padding: 12px 16px !important;
}
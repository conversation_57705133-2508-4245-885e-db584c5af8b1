import { Router } from 'express'
import {
  getDeviceList,
  getDeviceDetail,
  getDeviceListWithAlgorithm,
  updateDevice,
  getDeviceDigitalCore,
  getDeviceDigitalCoreDetail
} from '../controllers/device'
import { verifyToken } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'

const router = Router()

// 获取设备列表
router.get('/', asyncHandler(verifyToken), asyncHandler(getDeviceList))

// 获取带算法信息的设备列表
router.get('/with-algorithm', asyncHandler(verifyToken), asyncHandler(getDeviceListWithAlgorithm))

// 获取设备详情
router.get('/:id', asyncHandler(verifyToken), asyncHandler(getDeviceDetail))

// 获取设备数字岩芯信息
router.get('/:deviceId/digital-core', asyncHand<PERSON>(verifyToken), async<PERSON>and<PERSON>(getDeviceDigitalCore))

// 获取设备数字岩芯详情
router.get('/:deviceId/digital-core/:fileId', asyncHand<PERSON>(verifyToken), asyncHandler(getDeviceDigitalCoreDetail))

// 更新设备信息
router.put('/:id', asyncHandler(verifyToken), asyncHandler(updateDevice))

export default router

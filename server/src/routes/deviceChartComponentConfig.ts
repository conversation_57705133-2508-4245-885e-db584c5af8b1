import { Router } from 'express'
import {
  getAllDeviceChartConfigs,
  getDeviceChartConfigById,
  createDeviceChartConfig,
  updateDeviceChartConfig,
  deleteDeviceChartConfig,
  getDeviceChartConfigs,
  getDeviceChartConfigStatus,
  getDeviceEnabledCharts
} from '../controllers/deviceChartComponentConfig'
import { asyncHandler } from '../utils/asyncHandler'
import { verifyToken } from '../middleware/auth'

const router = Router()

// 获取所有设备图表组件配置
router.get('/', asyncHandler(getAllDeviceChartConfigs))

// 获取指定设备的所有图表组件配置
router.get('/device/:deviceId', asyncHandler(getDeviceChartConfigs))

// 获取设备的图表组件配置状态（所有组件及在当前设备的启用状态）
router.get('/device/:deviceId/status', asyncHandler(getDeviceChartConfigStatus))

// 获取设备已启用的图表组件（专用于设备详情页）
router.get('/device/:deviceId/enabled', asyncHandler(getDeviceEnabledCharts))

// 获取设备图表组件配置详情
router.get('/:id', asyncHandler(getDeviceChartConfigById))

// 创建设备图表组件配置
router.post('/', asyncHandler(verifyToken), asyncHandler(createDeviceChartConfig))

// 更新设备图表组件配置
router.put('/:id', asyncHandler(verifyToken), asyncHandler(updateDeviceChartConfig))

// 删除设备图表组件配置
router.delete('/:id', asyncHandler(verifyToken), asyncHandler(deleteDeviceChartConfig))

export default router 
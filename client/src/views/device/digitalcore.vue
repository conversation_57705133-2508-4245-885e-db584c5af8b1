<template>
  <div class="device-digitalcore-container page-container">
    <!-- 头部 -->
    <el-page-header
      class="mb-20"
      @back="goBack"
    >
      <template #content>
        <div class="header-content flex-center">
          <span class="device-name">{{ deviceData?.deviceName }}</span>
          <span class="device-title text-primary">数字岩心</span>
        </div>
      </template>
    </el-page-header>
    
    <div class="content-wrapper">
      <el-scrollbar>
        <div class="core-cards">
          <div 
            v-for="item in digitalCoreList" 
            :key="item.fileId"
            class="core-card"
          >
            <div class="card-content">
              <div class="card-header">
                <div class="file-name">
                  {{ item.fileName }}
                </div>
                <div class="file-time">
                  {{ item.startTime }}
                </div>
              </div>
              <div class="card-body">
                <div class="file-meta">
                  <div class="meta-item">
                    <span class="meta-label">孔号:</span>
                    <span class="meta-value">{{ item.holeNo || '未知' }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">深度:</span>
                    <span class="meta-value">{{ item.startDepth || 0 }}cm - {{ item.endDepth || 0 }}cm</span>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="viewDigitalCoreDetail(item.fileId)"
                >
                  查看
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-if="total > 0"
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
          />
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getDeviceDetail, getDeviceDigitalCore } from '@/api/device';
import { debouncedApiCall } from '@/utils/debounce';

export default defineComponent({
  name: 'DigitalCoreView',
  setup() {
    const route = useRoute();
    const router = useRouter();
    const deviceId = route.params.id as string;
    const deviceData = ref<any>(null);
    const loading = ref(true);
    const digitalCoreList = ref<any[]>([]);
    
    // 分页参数
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);
    
    // 获取设备数据
    const fetchDeviceData = async () => {
      try {
        loading.value = true;
        const response = await getDeviceDetail(deviceId);
        
        // 确保获取到数据并且API调用成功
        if (response.success && response.data) {
          // 合并数据，确保展示名称使用设备名称
          deviceData.value = response.data;
        } else {
          ElMessage.error('获取设备数据失败');
        }
      } catch (error) {
        ElMessage.error('获取设备数据失败');
        console.error('获取设备数据失败:', error);
      } finally {
        loading.value = false;
      }
    };
    
    // 获取数字岩芯数据
    const fetchDigitalCoreData = async (page = 1) => {
      try {
        loading.value = true;
        const response = await getDeviceDigitalCore(deviceId, {
          page,
          pageSize: pageSize.value
        });
        
        if (response.success && response.data) {
          digitalCoreList.value = response.data.list;
          total.value = response.data.total;
        } else {
          ElMessage.error('获取数字岩芯数据失败');
        }
      } catch (error) {
        ElMessage.error('获取数字岩芯数据失败');
        console.error('获取数字岩芯数据失败:', error);
      } finally {
        loading.value = false;
      }
    };
    
    // 查看数字岩芯详情
    const viewDigitalCoreDetail = (fileId: string) => {
      router.push(`/device/digitalcore/${deviceId}/detail/${fileId}`);
    };
    
    // 使用防抖的分页处理
    const handlePageChange = debouncedApiCall((page: number) => {
      currentPage.value = page;
      fetchDigitalCoreData(page);
    });
    
    // 返回上一页
    const goBack = () => {
      router.back();
    };
    
    onMounted(() => {
      fetchDeviceData();
      fetchDigitalCoreData();
    });

    // 组件卸载时清理防抖函数
    onUnmounted(() => {
      if (handlePageChange && typeof handlePageChange.cancel === 'function') {
        handlePageChange.cancel();
      }
    });

    return {
      deviceData,
      loading,
      digitalCoreList,
      currentPage,
      pageSize,
      total,
      goBack,
      viewDigitalCoreDetail,
      handlePageChange
    };
  }
});
</script>

<style scoped>
.device-digitalcore-container {
  height: calc(100vh - 120px);
}

.header-content {
  gap: 16px;
}

.device-name {
  font-size: 20px;
  font-weight: 600;
  color: var(--apple-text-primary);
}

.device-title {
  font-size: 14px;
  background-color: rgba(0, 113, 227, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
}

.content-wrapper {
  height: calc(100% - 60px);
}

.core-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 28px;
  padding: 32px;
}

.core-card {
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.04),
    0 1px 6px rgba(0, 0, 0, 0.03);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  border: none;
  width: calc(25% - 21px);
  margin-bottom: 0;
  backdrop-filter: blur(10px);
  position: relative;
}

.core-card:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.07),
    0 3px 10px rgba(0, 0, 0, 0.05);
}

.core-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #007AFF, #5AC8FA);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.core-card:hover::before {
  opacity: 1;
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-header {
  padding: 24px;
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.02);
}

.file-name {
  font-weight: 500;
  font-size: 16px;
  color: #262626;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: -0.01em;
}

.file-time {
  font-size: 12px;
  color: #909090;
  letter-spacing: 0.01em;
}

.card-body {
  padding: 20px 24px;
  flex-grow: 1;
  background-color: transparent;
}

.file-meta {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.meta-item {
  display: flex;
  align-items: flex-start;
}

.meta-label {
  font-size: 13px;
  color: #787878;
  margin-right: 10px;
  min-width: 40px;
}

.meta-value {
  font-size: 13px;
  color: #383838;
  font-weight: 450;
}

.card-footer {
  padding: 20px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid rgba(0, 0, 0, 0.02);
}

.card-footer .el-button {
  border-radius: 8px;
  padding: 8px 20px;
  font-weight: 500;
  letter-spacing: 0.02em;
  box-shadow: 0 2px 5px rgba(0, 113, 227, 0.1);
  transition: all 0.3s ease;
}

.card-footer .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 113, 227, 0.15);
}

.pagination-container {
  padding: 16px;
  display: flex;
  justify-content: center;
}
</style> 
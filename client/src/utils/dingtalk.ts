/**
 * 钉钉OAuth配置工具
 */

// 钉钉OAuth配置
export const getDingTalkConfig = () => {
  return {
    clientId: import.meta.env.VITE_DINGTALK_CLIENT_ID,
    redirectUri: import.meta.env.VITE_DINGTALK_REDIRECT_URI,
    scope: 'openid corpid',
    responseType: 'code',
    prompt: 'consent'
  }
}

// 生成钉钉OAuth授权URL
export const generateDingTalkAuthUrl = (): string => {
  const config = getDingTalkConfig()
  const params = new URLSearchParams({
    redirect_uri: config.redirectUri,
    response_type: config.responseType,
    client_id: config.clientId,
    scope: config.scope,
    prompt: config.prompt
  })
  
  return `https://login.dingtalk.com/oauth2/auth?${params.toString()}`
}

// 获取当前环境信息（用于调试）
export const getEnvironmentInfo = () => {
  return {
    mode: import.meta.env.MODE,
    dev: import.meta.env.DEV,
    prod: import.meta.env.PROD,
    redirectUri: import.meta.env.VITE_DINGTALK_REDIRECT_URI,
    clientId: import.meta.env.VITE_DINGTALK_CLIENT_ID
  }
}

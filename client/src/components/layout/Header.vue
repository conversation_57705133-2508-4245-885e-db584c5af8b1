<template>
  <div class="header">
    <div class="left-section">
      <div
        class="toggle-button"
        @click="toggleSidebar"
      >
        <el-icon>
          <Fold v-if="isCollapsed" />
          <Expand v-else />
        </el-icon>
      </div>
      <div class="breadcrumb">
        <span>海聚云装备</span>
      </div>
    </div>
    <div class="right-menu">
      <el-dropdown
        trigger="click"
        @command="handleCommand"
      >
        <div class="user-info">
          <el-avatar
            :size="32"
            class="user-avatar"
            :src="userStore.userInfo.avatar_url"
          >
            {{ getUserDisplayName().charAt(0) }}
          </el-avatar>
          <span class="username">{{ getUserDisplayName() }}</span>
          <el-icon class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="user-dropdown">
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              <span>个人信息</span>
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              <span>系统设置</span>
            </el-dropdown-item>
            <el-dropdown-item
              divided
              command="logout"
            >
              <el-icon><SwitchButton /></el-icon>
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'
import { ElMessage } from 'element-plus'
import { Fold, Expand, ArrowDown, User, Setting, SwitchButton } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 获取用户显示名称
const getUserDisplayName = () => {
  return userStore.userInfo.name || userStore.userInfo.username || '用户'
}

// const props = defineProps({
//   isCollapsed: {
//     type: Boolean,
//     default: false
//   }
// })

const emit = defineEmits(['toggle-sidebar'])

const toggleSidebar = () => {
  emit('toggle-sidebar')
}

// 添加下拉菜单处理函数
const handleCommand = async command => {
  if (command === 'logout') {
    try {
      await userStore.logoutAction()
      router.push('/login')
    } catch (error) {
      ElMessage({
        message: '退出登录失败',
        type: 'error',
        customClass: 'apple-message'
      })
    }
  } else if (command === 'profile') {
    ElMessage({
      message: '个人信息功能开发中',
      type: 'info',
      customClass: 'apple-message'
    })
  } else if (command === 'settings') {
    ElMessage({
      message: '系统设置功能开发中',
      type: 'info',
      customClass: 'apple-message'
    })
  }
}

// 添加生命周期钩子
onMounted(() => {
  // 组件挂载时的逻辑
  console.log('Header component mounted')
})

onBeforeUnmount(() => {
  // 组件卸载前的清理工作
  console.log('Header component will unmount')
})
</script>

<style scoped>
.header {
  height: 64px;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.left-section {
  display: flex;
  align-items: center;
}

.breadcrumb {
  margin-left: 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--apple-text-primary);
}

.toggle-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--apple-text-primary);
  border-radius: 6px;
  transition: var(--apple-transition);
}

.toggle-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--apple-primary);
}

.right-menu {
  display: flex;
  align-items: center;
}

.search-box {
  margin-right: 16px;
}

.search-input {
  width: 200px;
}

.search-input :deep(.el-input__inner) {
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.03);
  border: none;
}

.icon-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--apple-text-primary);
  margin-right: 16px;
  cursor: pointer;
  border-radius: 6px;
  transition: var(--apple-transition);
}

.icon-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--apple-primary);
}

.notification-badge :deep(.el-badge__content) {
  background-color: var(--apple-primary);
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  transition: var(--apple-transition);
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-avatar {
  background: linear-gradient(135deg, var(--apple-primary), #00a2ff);
  color: #fff;
  margin-right: 8px;
}

.username {
  font-size: 14px;
  color: var(--apple-text-primary);
  margin-right: 4px;
}

.dropdown-icon {
  font-size: 12px;
  color: var(--apple-text-secondary);
}

:deep(.user-dropdown) {
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 8px;
}

:deep(.user-dropdown .el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 6px;
}

:deep(.user-dropdown .el-dropdown-menu__item:hover) {
  background-color: rgba(0, 0, 0, 0.03);
}

:deep(.user-dropdown .el-dropdown-menu__item i) {
  margin-right: 8px;
  font-size: 16px;
}
</style>

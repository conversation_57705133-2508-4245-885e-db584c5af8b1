<template>
  <el-card
    class="matrix-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        <span>工况矩阵</span>
      </div>
    </template>
    <div
      ref="matrixChart"
      class="matrix-chart"
    />
  </el-card>
</template>

<script>
import { ref, watch, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'ConditionMatrixCard',
  props: {
    conditionMatrixData: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const matrixChart = ref(null)
    let matrixChartInstance = null
    let lastCalculatedHeight = 0 // 记录上次计算的高度，避免无限循环
    let resizeTimer = null // 防抖定时器

    // 计算图表容器的最佳高度 - 参考其他图表组件的实现
    const calculateChartHeight = () => {
      if (!matrixChart.value) {
        return 550 // 默认高度
      }

      // 获取父容器的实际高度 - 使用 el-card 作为参考容器
      const cardBodyElement = matrixChart.value.parentElement // el-card__body
      const cardElement = cardBodyElement?.parentElement // el-card

      // 使用 el-card 作为参考容器（第二层）
      const referenceElement = cardElement

      // 检查是否在flex布局中，如果是则使用默认高度
      const parentElement = cardElement?.parentElement
      const parentComputedStyle = parentElement ? window.getComputedStyle(parentElement) : null
      const isInFlexLayout = parentComputedStyle?.display === 'flex'

      // 1. 当父级高度没有设置时，或者在flex布局中时，使用默认高度
      if (!referenceElement || referenceElement.offsetHeight <= 100 || isInFlexLayout) {
        return 550 // 父级高度没有设置时的默认高度
      }

      // 2. 当父级高度设置了时，按照父级高度自动调整，不使用默认高度
      const parentHeight = referenceElement.offsetHeight

      // 3. 获取实际的header高度
      const headerElement = cardElement?.querySelector('.el-card__header')
      const actualHeaderHeight = headerElement ? headerElement.offsetHeight : 80

      // 3. 获取实际的padding（el-card__body的padding）
      const cardBodyStyles = window.getComputedStyle(cardBodyElement)
      const paddingTop = parseInt(cardBodyStyles.paddingTop) || 0
      const paddingBottom = parseInt(cardBodyStyles.paddingBottom) || 0
      const actualPadding = paddingTop + paddingBottom

      // 计算图表区域可用高度：完全按照父级容器调整
      const availableHeight = parentHeight - actualHeaderHeight - actualPadding

      // 如果计算出的高度太小（可能是DOM还没完全渲染），使用默认高度
      if (availableHeight < 300) {
        return 550 // 使用默认高度，而不是强制使用计算出的小值
      }

      return availableHeight
    }

    // 初始化工况矩阵图表
    const initChart = () => {
      if (matrixChartInstance) {
        matrixChartInstance.dispose()
      }

      nextTick(() => {
        // 确保DOM元素存在再初始化
        if (!matrixChart.value) {
          console.warn('工况矩阵图表DOM元素不存在')
          return
        }

        // 动态计算并设置图表高度
        const chartHeight = calculateChartHeight()
        lastCalculatedHeight = chartHeight // 记录初始高度
        matrixChart.value.style.height = chartHeight + 'px'

        matrixChartInstance = echarts.init(matrixChart.value)

        // 使用API返回的工况矩阵数据
        const conditionData = props.conditionMatrixData || []

        // 散点图矩阵使用5个关键维度
        const scatterSchema = [
          { name: '钻孔深度CM', index: 0, text: '钻孔深度CM' },
          { name: '旋转扭矩', index: 1, text: '旋转扭矩' },
          { name: '推进力', index: 2, text: '推进力' },
          { name: '旋转速度', index: 3, text: '旋转速度' },
          { name: '钻进速度', index: 4, text: '钻进速度' }
        ]

        // 从API数据准备散点图数据 - 根据工况矩阵数据结构
        const scatterData = conditionData.map(item => {
          return [
            item[1], // 钻孔深度 [1]
            item[2], // 旋转扭矩 [2]
            item[3], // 推进力 [3]
            item[4], // 旋转速度 [4]
            item[5], // 钻进速度 [5]
            item[8]  // 地质预警 [8]
          ]
        })

        // 岩石强度级别字符串映射到数值
        const rockStrengthMap = {
          '极坚固': 90,
          '很坚固': 70,
          '坚固': 50,
          '比较坚固': 40,
          '中等坚固': 30,
          '较软': 15,
          '软岩': 10,
          '土体': 5
        };
        
        // 添加一个存储原始岩石强度字符串的数组，用于显示
        const rockStrengthTexts = conditionData.map(item => 
          item[6] && typeof item[6] === 'string' ? item[6] : '未知');
          
        // 确保有一些预设的岩石强度类型，即使数据中没有
        const defaultRockStrengths = ['极坚固', '很坚固', '坚固', '比较坚固', '中等坚固', '较软'];
        
        // 获取所有唯一的岩石强度类型，结合默认类型确保至少有一些显示
        const dataRockStrengths = [...new Set(rockStrengthTexts.filter(text => text !== '未知'))];
        const uniqueRockStrengths = dataRockStrengths.length > 0 ? 
          dataRockStrengths : defaultRockStrengths;
        
        // 记录数值到类别的映射关系，用于后续显示
        const valueToCategory = {};
        uniqueRockStrengths.forEach((type, index) => {
          valueToCategory[rockStrengthMap[type] || index] = type;
        });

        // 为平行坐标系准备完整的数据
        const parallelData = conditionData.map(item => {
          // 获取岩石强度级别的数值表示，如果是字符串则使用映射值，否则使用原数值
          let rockStrengthValue = 0;
          if(item[6] && typeof item[6] === 'string') {
            rockStrengthValue = rockStrengthMap[item[6]] || 0;
          } else if(item[6] && !isNaN(Number(item[6]))) {
            rockStrengthValue = Number(item[6]);
          }
          
          // 确保每个值都是数值类型，防止类型不匹配导致的显示问题
          return [
            Number(item[0]) || 0, // 工作模式 [0]
            Number(item[1]) || 0, // 钻孔深度 [1]
            Number(item[2]) || 0, // 旋转扭矩 [2]
            Number(item[3]) || 0, // 推进力 [3]
            Number(item[4]) || 0, // 旋转速度 [4]
            Number(item[5]) || 0, // 钻进速度 [5]
            rockStrengthValue    // 岩石强度 [6] - 已映射为数值
          ]
        });

        // 散点图常量
        const CATEGORY_DIM_COUNT = scatterSchema.length // 5个维度
        const CATEGORY_DIM = 5 // 地质预警的维度索引

        // 提取散点图数据的辅助函数
        function retrieveScatterData(rawData, i, j) {
          return rawData.map(function (item) {
            return [item[i], item[j], item[CATEGORY_DIM]]
          })
        }

        // 生成散点矩阵配置
        function generateGrids() {
          let index = 0
          const grid = []
          const xAxis = []
          const yAxis = []
          const series = []

          // 基础参数设置
          const BASE_LEFT = 5
          const BASE_TOP = 5
          const GAP = 1

          // 计算网格尺寸（5x5矩阵）
          const GRID_WIDTH = (100 - BASE_LEFT - GAP) / CATEGORY_DIM_COUNT - GAP
          const GRID_HEIGHT = GRID_WIDTH // 保持正方形比例

          for (let i = 0; i < CATEGORY_DIM_COUNT; i++) {
            for (let j = 0; j < CATEGORY_DIM_COUNT; j++) {
              // 只显示下三角矩阵
              if (j >= i) {
                continue
              }

              grid.push({
                left: BASE_LEFT + i * (GRID_WIDTH + GAP) + '%',
                top: BASE_TOP + j * (GRID_HEIGHT + GAP) + '%',
                width: GRID_WIDTH + '%',
                height: GRID_HEIGHT + '%',
                show: true,
                borderWidth: 0.5,
                borderColor: '#eee'
              })

              xAxis.push({
                name: scatterSchema[i].text,
                nameLocation: 'middle',
                nameGap: 30,
                nameTextStyle: {
                  fontSize: 10,
                  color: '#666',
                  padding: [10, 0, 0, 0]
                },
                splitNumber: 5,
                position: 'top',
                axisLine: {
                  show: true,
                  onZero: false,
                  lineStyle: {
                    color: '#ddd',
                    width: 0.5
                  }
                },
                axisTick: {
                  show: true,
                  inside: true,
                  length: 2,
                  lineStyle: {
                    width: 0.5,
                    color: '#ddd'
                  }
                },
                axisLabel: {
                  show: true,
                  fontSize: 8,
                  color: '#999',
                  margin: 4
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: '#eee',
                    width: 0.5,
                    type: 'solid'
                  }
                },
                type: 'value',
                gridIndex: index,
                scale: true,
                interval: 'auto',
                minInterval: 1
              })

              yAxis.push({
                name: scatterSchema[j].text,
                nameLocation: 'end',
                nameGap: 10,
                nameTextStyle: {
                  fontSize: 10,
                  color: '#666',
                  padding: [0, 0, 0, 0]
                },
                splitNumber: 5,
                position: 'right',
                axisLine: {
                  show: true,
                  onZero: false,
                  lineStyle: {
                    color: '#ddd',
                    width: 0.5
                  }
                },
                axisTick: {
                  show: true,
                  inside: true,
                  length: 2,
                  lineStyle: {
                    width: 0.5,
                    color: '#ddd'
                  }
                },
                axisLabel: {
                  show: true,
                  fontSize: 8,
                  color: '#999',
                  margin: 4
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: '#eee',
                    width: 0.5,
                    type: 'solid'
                  }
                },
                type: 'value',
                gridIndex: index,
                scale: true,
                interval: 'auto',
                minInterval: 1
              })

              series.push({
                type: 'scatter',
                symbolSize: 1,
                symbol: 'circle',
                itemStyle: {
                  opacity: 0.6
                },
                emphasis: {
                  itemStyle: {
                    opacity: 1
                  }
                },
                xAxisIndex: index,
                yAxisIndex: index,
                data: retrieveScatterData(scatterData, i, j),
                progressive: 500,
                progressiveThreshold: 3000,
                large: true,
                largeThreshold: 100
              })

              index++
            }
          }

          return { grid, xAxis, yAxis, series }
        }

        const gridOption = generateGrids()

        // 平行坐标系轴线配置
        const parallelAxisConfig = [
          {
            dim: 0,
            name: '工作模式',
            nameTextStyle: {
              color: '#d14a61'
            }
          },
          {
            dim: 1,
            name: '钻孔深度CM',
            nameTextStyle: {
              color: '#5793f3'
            }
          },
          {
            dim: 2,
            name: '旋转扭矩',
            nameTextStyle: {
              color: '#d7504b'
            }
          },
          {
            dim: 3,
            name: '推进力',
            nameTextStyle: {
              color: '#6b6b6b'
            }
          },
          {
            dim: 4,
            name: '旋转速度',
            nameTextStyle: {
              color: '#c4ccd3'
            }
          },
          {
            dim: 5,
            name: '钻进速度',
            nameTextStyle: {
              color: '#40a863'
            }
          },
          {
            dim: 6,
            name: '岩石强度级别',
            nameTextStyle: {
              color: '#ca8622'
            },
            axisLabel: {
              show: true,
              formatter: function(value) {
                if(value >= 80) return '极坚固';
                if(value >= 60) return '很坚固';
                if(value >= 45) return '坚固';
                if(value >= 35) return '比较坚固';
                if(value >= 20) return '中等坚固';
                if(value >= 10) return '较软';
                if(value >= 5) return '软岩';
                return '土体';
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#ddd',
                width: 0.8
              }
            },
            axisTick: {
              show: true,
              interval: function(index, value) {
                return [5, 10, 20, 35, 45, 60, 80].indexOf(value) >= 0;
              }
            }
          }
        ];
        
        const option = {
          animation: false,
          visualMap: {
            type: 'piecewise',
            categories: ['无', '低强度岩层', '水压异常', '高风险预警'],
            dimension: CATEGORY_DIM,
            orient: 'horizontal',
            top: 20,
            left: 10,
            height: 200,
            inRange: {
              color: ['#1ba357', '#1191F0', '#FFA500', '#ee5c5c']
            },
            outOfRange: {
              color: '#ddd'
            },
            seriesIndex: gridOption.series.map((_, idx) => idx)
          },
          tooltip: {
            trigger: 'item',
            formatter: function(params) {
              if (params.seriesType === 'parallel') {
                const index = params.dataIndex;
                const rockStrengthText = rockStrengthTexts[index] || '未知';
                
                return `
                  <div style="padding: 5px 8px">
                    <div style="margin-bottom: 5px;font-weight:bold;border-bottom:1px solid #ddd;padding-bottom:5px">工况参数</div>
                    <div style="line-height:1.6em">
                      <span style="display:inline-block;width:90px;color:#999">工作模式:</span> ${params.value[0]}<br/>
                      <span style="display:inline-block;width:90px;color:#999">钻孔深度:</span> ${params.value[1]}CM<br/>
                      <span style="display:inline-block;width:90px;color:#999">旋转扭矩:</span> ${params.value[2]}<br/>
                      <span style="display:inline-block;width:90px;color:#999">推进力:</span> ${params.value[3]}<br/>
                      <span style="display:inline-block;width:90px;color:#999">旋转速度:</span> ${params.value[4]}<br/>
                      <span style="display:inline-block;width:90px;color:#999">钻进速度:</span> ${params.value[5]}<br/>
                      <span style="display:inline-block;width:90px;color:#999">岩石强度级别:</span> <span style="color:#27a9e3;font-weight:bold">${rockStrengthText}</span>
                    </div>
                  </div>
                `;
              }
              return '';
            },
            backgroundColor: 'rgba(255,255,255,0.95)',
            borderColor: '#ddd',
            borderWidth: 1,
            textStyle: {
              color: '#333'
            },
            padding: 10,
            extraCssText: 'box-shadow: 0 0 8px rgba(0, 0, 0, 0.2); border-radius: 4px;'
          },
          grid: gridOption.grid,
          xAxis: gridOption.xAxis,
          yAxis: gridOption.yAxis,
          
          // 平行坐标系部分
          parallel: {
            left: '8%',
            right: '35%',
            bottom: 20,
            top: '70%',
            width: '55%',
            parallelAxisDefault: {
              type: 'value',
              nameLocation: 'end',
              nameGap: 12,
              nameTextStyle: {
                fontSize: 12,
                color: '#666',
                fontWeight: 'normal'
              },
              axisLine: {
                lineStyle: {
                  color: '#aaa',
                  width: 1
                }
              },
              axisTick: {
                show: true,
                lineStyle: {
                  color: '#aaa'
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#ddd',
                  width: 0.8,
                  type: 'dashed'
                }
              },
              axisLabel: {
                fontSize: 11,
                show: true,
                formatter: function (value) {
                  if (typeof value === 'number') {
                    return value.toFixed(0)
                  }
                  return value
                }
              }
            }
          },
          parallelAxis: parallelAxisConfig,

          series: [
            ...gridOption.series,
            {
              type: 'parallel',
              smooth: true,
              lineStyle: {
                width: 0.8,
                opacity: 0.35,
                color: (params) => {
                  const index = params.dataIndex;
                  const rockStrengthText = rockStrengthTexts[index] || '未知';
                  
                  // 使用浅绿色系配色
                  switch(rockStrengthText) {
                    case '极坚固': return 'rgba(165, 214, 167, 0.7)'; // 浅绿色
                    case '很坚固': return 'rgba(129, 199, 132, 0.7)'; // 浅绿色
                    case '坚固': return 'rgba(102, 187, 106, 0.7)'; // 浅绿色
                    case '比较坚固': return 'rgba(76, 175, 80, 0.7)'; // 浅绿色
                    case '中等坚固': return 'rgba(67, 160, 71, 0.7)'; // 浅绿色
                    case '较软': return 'rgba(56, 142, 60, 0.7)'; // 浅绿色
                    case '软岩': return 'rgba(46, 125, 50, 0.7)'; // 浅绿色
                    case '土体': return 'rgba(27, 94, 32, 0.7)'; // 浅绿色
                    default: return 'rgba(200, 230, 201, 0.6)'; // 更浅的绿色
                  }
                }
              },
              emphasis: {
                lineStyle: {
                  width: 2,
                  opacity: 0.7,
                  shadowBlur: 4,
                  shadowColor: 'rgba(0,0,0,0.2)'
                }
              },
              data: parallelData
            }
          ]
        }

        matrixChartInstance.setOption(option)
      })
    }

    // 监听数据变化
    watch(() => props.conditionMatrixData, () => {
      nextTick(initChart)
    }, { deep: true })

    // 组件挂载时初始化图表
    onMounted(() => {
      // 延迟初始化，确保DOM完全渲染和父容器尺寸确定
      setTimeout(() => {
        initChart()
      }, 100)

      // 再次延迟初始化，处理可能的异步布局变化
      setTimeout(() => {
        if (matrixChartInstance) {
          const newHeight = calculateChartHeight()
          if (matrixChart.value) {
            matrixChart.value.style.height = newHeight + 'px'
            matrixChartInstance.resize()
          }
        }
      }, 500)

      // 添加窗口resize监听
      window.addEventListener('resize', handleResize)

      // 添加ResizeObserver监听容器尺寸变化（用于模板模式）
      if (window.ResizeObserver) {
        setTimeout(() => {
          if (matrixChart.value) {
            const resizeObserver = new ResizeObserver(() => {
              handleResize()
            })

            // 只观察外层容器，避免监听图表本身导致无限循环
            const cardElement = matrixChart.value.parentElement?.parentElement // el-card
            const outerElement = cardElement?.parentElement // 外层容器

            // 优先监听外层容器，如果没有则监听card容器
            const targetElement = outerElement || cardElement

            if (targetElement) {
              resizeObserver.observe(targetElement)
            }

            // 在组件卸载时清理observer
            onUnmounted(() => {
              resizeObserver.disconnect()
            })
          }
        }, 200)
      }
    })

    // 组件卸载前清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)

      // 清理防抖定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer)
        resizeTimer = null
      }

      if (matrixChartInstance) {
        matrixChartInstance.dispose()
        matrixChartInstance = null
      }
    })

    // 处理窗口大小变化
    const handleResize = () => {
      // 清除之前的定时器，实现防抖
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }

      resizeTimer = setTimeout(() => {
        if (matrixChartInstance && matrixChart.value) {
          // 重新计算图表高度以适应容器变化
          const newHeight = calculateChartHeight()

          // 只有当高度变化超过10px时才更新，避免无限循环
          if (Math.abs(newHeight - lastCalculatedHeight) > 10) {
            lastCalculatedHeight = newHeight
            matrixChart.value.style.height = newHeight + 'px'

            // 延迟执行resize，确保DOM更新完成
            setTimeout(() => {
              if (matrixChartInstance) {
                matrixChartInstance.resize()
              }
            }, 100)
          }
        }
      }, 150) // 150ms防抖延迟
    }

    return {
      matrixChart
    }
  }
}
</script>

<style scoped>
.matrix-card {
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
}

.matrix-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.matrix-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.sub-card-header span:first-child {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  position: relative;
}

.sub-card-header span:first-child::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.matrix-chart {
  width: 100%;
}
</style> 
<template>
  <div class="app-wrapper">
    <div
      class="sidebar-container"
      :style="{ width: isCollapsed ? '64px' : '240px' }"
    >
      <Sidebar :is-collapsed="isCollapsed" />
    </div>
    <div
      class="main-wrapper"
      :style="{ marginLeft: isCollapsed ? '64px' : '240px' }"
    >
      <Header
        :is-collapsed="isCollapsed"
        @toggle-sidebar="toggleSidebar"
      />
      <div class="main-content">
        <transition
          name="apple-fade"
          mode="out-in"
        >
          <router-view v-if="isRouterViewActive" />
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted } from 'vue'
import Header from './Header.vue'
import Sidebar from './Sidebar.vue'

const isCollapsed = ref(false)
const isRouterViewActive = ref(true)
let sidebarToggleTimer = null

const toggleSidebar = async () => {
  // 清理之前的定时器
  if (sidebarToggleTimer) {
    clearTimeout(sidebarToggleTimer)
  }

  // 临时移除 router-view 以减少级联重绘
  isRouterViewActive.value = false

  // 等待DOM更新
  await nextTick()

  // 切换侧边栏状态
  isCollapsed.value = !isCollapsed.value

  // 等待过渡完成后再恢复 router-view
  sidebarToggleTimer = setTimeout(() => {
    isRouterViewActive.value = true
    // 手动触发 resize 事件
    window.dispatchEvent(new Event('resize'))
  }, 300) // 略大于过渡时间
}

onMounted(() => {
  // 确保初始加载时布局正确
  window.dispatchEvent(new Event('resize'))
})

onUnmounted(() => {
  // 清理定时器
  if (sidebarToggleTimer) {
    clearTimeout(sidebarToggleTimer)
    sidebarToggleTimer = null
  }
})
</script>

<style scoped>
.app-wrapper {
  height: 100%;
  width: 100%;
  display: flex;
  overflow: hidden;
  position: relative;
  background-color: var(--apple-background);
}

.sidebar-container {
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1001;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  transition: width 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}

.main-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  transition: margin-left 0.3s cubic-bezier(0.34, 0.69, 0.1, 1);
}

.main-content {
  flex: 1;
  overflow: auto;
  padding: 24px;
  background-color: var(--apple-background);
  position: relative;
}

/* 自定义滚动条 */
.main-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}
</style>

import { Router } from 'express'
import { verifyToken } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'
import { validateDataQuery, validateDataQueryWithAlgorithms } from '../middleware/validation'

// 从controllers/data.ts导入当前实际使用的方法
import {
  getFieldMappings,
  queryData,
  queryDataWithAlgorithms,
  getLatestDeviceData,
  queryDataByDepth
} from '../controllers/data'

const router = Router()

// 使用认证中间件保护数据接口
router.use(verifyToken)

// 获取字段映射表
router.get('/field-mappings', asyncHandler(getFieldMappings))

// 统一数据查询接口
router.post('/query', validateDataQuery, asyncHandler(queryData))

// 查询原始数据并自动调用指定算法
router.post('/query-with-algorithms', validateDataQueryWithAlgorithms, asyncHandler(queryDataWithAlgorithms))

// 基于深度范围查询数据
router.post('/query-by-depth', asyncHandler(queryDataByDepth))

// 获取设备最新一条数据
router.get('/latest', asyncHandler(getLatestDeviceData))

export default router 
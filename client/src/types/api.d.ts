// 通用响应格式
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页请求参数
export interface PaginationParams {
  page: number
  pageSize: number
}

// 分页响应格式
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  role: 'admin' | 'user'
  createdAt: string
  updatedAt: string
}

// 设备相关类型
export interface Device {
  id: string
  type: string
  typeName: string
  name: string
  serialNumber: string
  lastDataTime: string
  lastFileTime: string
  productKey?: string
  macAddress?: string
  createdAt?: string
  modifiedAt?: string
}

// 算法相关类型
export interface Arithmetic {
  id: string
  type: number
  typeName?: string
  name: string
  productKey: string
  ossPath?: string
  content?: string
  md5?: string
  isDefault: boolean
  createdAt: string
  modifiedAt?: string
}

// 登录请求参数
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应数据
export interface LoginResponse {
  message: string
  user: {
    id: string
    username: string
    name: string
    role: string
  }
  // token现在通过HttpOnly Cookie传递，不在响应中返回
}

export interface LogoutResponse {
  message: string
}

export interface UserInfo {
  id: string
  username: string
  name: string
  role: string
  avatar_url?: string
}

export interface ExampleData {
  id: number
  name: string
}

export interface ExampleResponse {
  message: string
  timestamp: string
  data: ExampleData[]
}

// 文件相关类型
export interface File {
  id: string
  device_sn: string
  file_name: string
  file_size: number
  created_at: string
}

// 文件列表响应
export interface FileListResponse {
  success: boolean
  data: {
    total: number
    list: File[]
  }
}

// 文件详情响应
export interface FileDetailResponse {
  success: boolean
  data: File
}

// 算法列表响应
export interface ArithmeticListResponse {
  success: boolean
  data: {
    total: number
    list: Arithmetic[]
  }
}

// 算法详情响应
export interface ArithmeticDetailResponse {
  success: boolean
  data: Arithmetic
}

// 统计数据响应
export interface StatsResponse {
  success: boolean
  data: {
    deviceCount: number
    dataRows: number
    cleanedCount: number
    totalSize: number
    formattedSize: string
  }
}

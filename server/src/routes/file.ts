import { Router } from 'express'
import {
  getFileList,
  getFileDetail,
  uploadFile,
  updateFile,
  deleteFile,
  getStats,
  getDeviceFiles,
  getFileOriginalData
} from '../controllers/file'
import { verifyToken } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'

const router = Router()

// 获取统计数据
router.get('/stats', asyncHandler(verifyToken), asyncHandler(getStats))

// 获取文件列表
router.get('/', asyncHandler(verifyToken), asyncHandler(getFileList))

// 获取设备文件列表
router.get('/device/:deviceId', asyncHandler(verifyToken), asyncHandler(getDeviceFiles))

// 获取文件原始数据
router.get('/:fileId/original-data', asyncHandler(verifyToken), asyncHandler(getFileOriginalData))

// 获取文件详情
router.get('/:id', asyncHandler(verifyToken), asyncHandler(getFileDetail))

// 上传文件
router.post('/', asyncHandler(verifyToken), asyncHandler(uploadFile))

// 更新文件信息
router.put('/:id', asyncHandler(verifyToken), asyncHandler(updateFile))

// 删除文件
router.delete('/:id', asyncHandler(verifyToken), asyncHandler(deleteFile))

export default router

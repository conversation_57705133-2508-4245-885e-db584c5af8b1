import { Request, Response, NextFunction } from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { AppError } from './error'

/**
 * 处理验证结果的中间件
 */
export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg).join(', ')
    return next(new AppError(`输入验证失败: ${errorMessages}`, 400))
  }
  next()
}

/**
 * 用户登录验证规则
 */
export const validateLogin = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('密码长度必须在6-128个字符之间'),
    // 暂时禁用复杂密码验证
    // .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    // .withMessage('密码必须包含至少一个小写字母、一个大写字母和一个数字'),
  
  handleValidationErrors
]

/**
 * 用户注册验证规则
 */
export const validateRegister = [
  body('username')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('密码长度必须在6-128个字符之间'),
    // 暂时禁用复杂密码验证
    // .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    // .withMessage('密码必须包含至少一个小写字母、一个大写字母、一个数字和一个特殊字符'),
  
  body('name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('姓名长度必须在1-100个字符之间')
    .escape(), // 防止XSS
  
  handleValidationErrors
]

/**
 * ID参数验证规则
 */
export const validateId = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID必须是正整数'),
  
  handleValidationErrors
]

/**
 * 设备ID验证规则
 */
export const validateDeviceId = [
  param('deviceId')
    .isInt({ min: 1 })
    .withMessage('设备ID必须是正整数'),
  
  handleValidationErrors
]

/**
 * 文件上传验证规则
 */
export const validateFileUpload = [
  body('file_name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('文件名长度必须在1-255个字符之间')
    .matches(/^[a-zA-Z0-9._-]+$/)
    .withMessage('文件名只能包含字母、数字、点、下划线和连字符'),
  
  body('content')
    .optional()
    .isLength({ max: 10485760 }) // 10MB
    .withMessage('文件内容不能超过10MB'),
  
  handleValidationErrors
]

/**
 * 算法创建验证规则
 */
export const validateArithmetic = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('算法名称长度必须在1-100个字符之间')
    .matches(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/)
    .withMessage('算法名称只能包含字母、数字、下划线和中文'),
  
  body('type')
    .isInt({ min: 0, max: 1 })
    .withMessage('算法类型必须是0或1'),
  
  body('product_key')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('产品类型长度必须在1-50个字符之间')
    .matches(/^[A-Z]+$/)
    .withMessage('产品类型只能包含大写字母'),
  
  body('content')
    .optional()
    .isLength({ max: 1048576 }) // 1MB
    .withMessage('算法内容不能超过1MB'),
  
  handleValidationErrors
]

/**
 * 数据查询验证规则
 */
export const validateDataQuery = [
  body('deviceId')
    .isInt({ min: 1 })
    .withMessage('设备ID必须是正整数'),

  body('fileId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('文件ID必须是正整数'),

  body('startTime')
    .optional()
    .isISO8601()
    .withMessage('开始时间必须是有效的ISO8601格式'),

  body('endTime')
    .optional()
    .isISO8601()
    .withMessage('结束时间必须是有效的ISO8601格式'),

  body('limit')
    .optional()
    .isInt({ min: 1, max: 10000 })
    .withMessage('限制数量必须在1-10000之间'),

  body('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('偏移量必须是非负整数'),

  handleValidationErrors
]

/**
 * 带算法的数据查询验证规则
 */
export const validateDataQueryWithAlgorithms = [
  body('deviceId')
    .isInt({ min: 1 })
    .withMessage('设备ID必须是正整数'),

  body('functions')
    .notEmpty()
    .withMessage('functions参数不能为空')
    .isString()
    .withMessage('functions参数必须是字符串')
    .isLength({ min: 1, max: 1000 })
    .withMessage('functions参数长度必须在1-1000字符之间')
    .matches(/^[a-zA-Z0-9_,\s]+$/)
    .withMessage('functions参数只能包含字母、数字、下划线和逗号'),

  body('fileId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('文件ID必须是正整数'),

  body('startTime')
    .optional()
    .isISO8601()
    .withMessage('开始时间必须是有效的ISO8601格式'),

  body('endTime')
    .optional()
    .isISO8601()
    .withMessage('结束时间必须是有效的ISO8601格式'),

  body('holeNo')
    .optional()
    .isString()
    .withMessage('孔号必须是字符串')
    .isLength({ min: 1, max: 50 })
    .withMessage('孔号长度必须在1-50字符之间')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('孔号只能包含字母、数字、下划线和连字符'),

  body('limit')
    .optional()
    .isInt({ min: 1, max: 10000 })
    .withMessage('限制数量必须在1-10000之间'),

  body('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('偏移量必须是非负整数'),

  handleValidationErrors
]

/**
 * 分页查询验证规则
 */
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  
  handleValidationErrors
]

/**
 * 通用字符串清理函数
 */
export const sanitizeString = (str: string): string => {
  return str
    .trim()
    .replace(/[<>]/g, '') // 移除潜在的HTML标签
    .replace(/javascript:/gi, '') // 移除JavaScript协议
    .replace(/on\w+=/gi, '') // 移除事件处理器
}

/**
 * SQL注入防护 - 清理可能的SQL关键字
 */
export const sanitizeSqlInput = (str: string): string => {
  const sqlKeywords = [
    'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER',
    'UNION', 'OR', 'AND', 'WHERE', 'FROM', 'JOIN', '--', ';', '/*', '*/'
  ]
  
  let cleaned = str
  sqlKeywords.forEach(keyword => {
    const regex = new RegExp(keyword, 'gi')
    cleaned = cleaned.replace(regex, '')
  })
  
  return cleaned.trim()
}

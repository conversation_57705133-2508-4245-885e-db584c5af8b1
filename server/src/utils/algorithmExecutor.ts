import ivm from 'isolated-vm'
import Arithmetic from '../models/arithmetic'
import { AppError } from '../middleware/error'
import { securityConfig } from '../config/security'
import config from '../config'
import { CodeSecurityAnalyzer, SecurityAnalysisResult } from './codeSecurityAnalyzer'

/**
 * 算法执行器类
 */
export class AlgorithmExecutor {
  private isolate!: ivm.Isolate
  private context!: ivm.Context
  private jail: any
  private algorithm: Arithmetic | null = null
  private loadedAlgorithmId: number | null = null
  // 修改缓存结构,保存算法对象和MD5值
  private algorithmsCache: Map<number, {md5: string, loadTime: Date}> = new Map()
  // 添加脚本缓存,保存处理后的算法代码
  private scriptsCache: Map<number, string> = new Map()
  private retryCount: number = 0
  private maxRetries: number = 3
  private readonly MEMORY_LIMIT = Math.floor(config.algorithm.maxMemory / (1024 * 1024)) // 从配置获取，转换为MB
  private readonly BATCH_SIZE = 5000 // 每批处理的数据量
  private readonly GC_THRESHOLD = 0.7 // GC触发阈值(70%)
  private lastGCTime: number = 0
  private readonly GC_INTERVAL = 5000 // GC最小间隔(5秒)
  private readonly MAX_EXECUTION_TIME = config.algorithm.maxExecutionTime // 从配置获取最大执行时间
  private readonly MAX_CODE_SIZE = config.algorithm.maxCodeSize // 从配置获取最大代码大小
  private readonly codeSecurityAnalyzer = new CodeSecurityAnalyzer() // AST安全分析器

  constructor() {
    this.createIsolate()
  }

  /**
   * 创建新的隔离环境
   */
  private createIsolate(): void {
    try {
      this.isolate = new ivm.Isolate({ 
        memoryLimit: this.MEMORY_LIMIT,
        onCatastrophicError: (error) => {
          console.error('隔离环境发生灾难性错误:', error)
          this.recreateIsolate()
        }
      })
      this.context = this.isolate.createContextSync()
      this.jail = this.context.global
      this.retryCount = 0
    } catch (error) {
      console.error('创建隔离环境失败:', error)
      throw new AppError('创建隔离环境失败', 500)
    }
  }

  /**
   * 重新创建隔离环境
   */
  private recreateIsolate(): void {
    try {
      // 先销毁旧的隔离环境
      if (this.isolate) {
        this.isolate.dispose()
      }
      // 创建新的隔离环境
      this.createIsolate()
    } catch (error) {
      console.error('重新创建隔离环境失败:', error)
      throw new AppError('重新创建隔离环境失败', 500)
    }
  }

  /**
   * 处理算法代码,移除ES6模块语法并进行安全检查
   * @param code 原始代码
   */
  private preprocessCode(code: string): string {
    // 1. 首先进行AST安全分析
    const securityResult = this.codeSecurityAnalyzer.analyzeCode(code)

    if (!securityResult.isSecure) {
      const criticalViolations = securityResult.violations.filter(v => v.severity === 'CRITICAL')
      const highViolations = securityResult.violations.filter(v => v.severity === 'HIGH')

      if (criticalViolations.length > 0 || securityResult.riskLevel === 'CRITICAL') {
        const errorMessage = `代码包含严重安全风险 (风险等级: ${securityResult.riskLevel}, 安全分数: ${securityResult.score})\n` +
          `关键违规: ${criticalViolations.map(v => v.message).join('; ')}`
        throw new AppError(errorMessage, 400)
      }

      if (highViolations.length > 0 || securityResult.riskLevel === 'HIGH') {
        const errorMessage = `代码包含高安全风险 (风险等级: ${securityResult.riskLevel}, 安全分数: ${securityResult.score})\n` +
          `高风险违规: ${highViolations.map(v => v.message).join('; ')}`
        throw new AppError(errorMessage, 400)
      }

      // 记录中低风险违规但允许继续执行
      if (securityResult.violations.length > 0) {
        console.warn(`算法代码安全警告 (风险等级: ${securityResult.riskLevel}, 安全分数: ${securityResult.score}):`,
          securityResult.violations.map(v => v.message))
      }
    }

    // 2. 传统正则检查作为补充
    const legacyCheck = securityConfig.algorithm.containsForbiddenAPI(code)
    if (!legacyCheck.isValid) {
      throw new AppError(`代码包含禁止的API: ${legacyCheck.forbiddenAPIs.join(', ')}`, 400)
    }

    // 3. 移除ES6模块语法
    let processedCode = code.replace(/export\s+(default|const|let|var|function|class)\s+/g, '$1 ');

    // 移除单独的export {xxx}语句
    processedCode = processedCode.replace(/export\s+\{[^}]*\};?/g, '');

    // 移除import语句
    processedCode = processedCode.replace(/import\s+.*?from\s+['"].*?['"];?/g, '');
    processedCode = processedCode.replace(/import\s+['"].*?['"];?/g, '');

    // 添加调试日志
    console.log(`代码预处理完成 - 安全分数: ${securityResult.score}, 风险等级: ${securityResult.riskLevel}`);

    return processedCode;
  }

  /**
   * 加载算法
   * @param algorithm 算法对象
   */
  async loadAlgorithm(algorithm: Arithmetic): Promise<void> {
    if (!algorithm.content) {
      throw new AppError('算法内容为空', 400)
    }

    try {
      // 检查算法是否已经加载并且MD5相同
      const cachedAlgorithm = this.algorithmsCache.get(algorithm.id)
      
      // 如果没有缓存的处理后代码，或算法已更新，则重新处理
      let processedContent: string
      const needReprocess = !this.scriptsCache.has(algorithm.id) || 
                           !cachedAlgorithm || 
                           cachedAlgorithm.md5 !== algorithm.md5
                           
      if (needReprocess) {
        processedContent = this.preprocessCode(algorithm.content)
        this.scriptsCache.set(algorithm.id, processedContent)
      } else {
        processedContent = this.scriptsCache.get(algorithm.id) || ''
      }
      
      // 判断是否需要重新创建环境和加载
      if (!cachedAlgorithm || 
          cachedAlgorithm.md5 !== algorithm.md5 || 
          this.loadedAlgorithmId !== algorithm.id) {
        
        console.log(`算法 ${algorithm.id} 需要重新加载到隔离环境`)
        
        // 重新创建环境并加载
        this.recreateIsolate()
        
        // 在隔离环境中执行算法代码
        const script = this.isolate.compileScriptSync(processedContent)
        await script.run(this.context)
        
        // 更新缓存信息
        this.algorithmsCache.set(algorithm.id, {
          md5: algorithm.md5 || '',
          loadTime: new Date()
        })
        
        console.log(`算法 ${algorithm.id} 加载成功,MD5: ${algorithm.md5}`)
      } else {
        console.log(`算法 ${algorithm.id} 已经加载,MD5未变化,使用缓存版本`)
      }
      
      // 无论是否重新加载，都更新当前算法引用
      this.algorithm = algorithm
      this.loadedAlgorithmId = algorithm.id
      
    } catch (error: any) {
      // 如果是隔离环境错误，尝试重新创建并重试
      if (error.message.includes('Isolated is disposed') && this.retryCount < this.maxRetries) {
        console.log(`隔离环境错误，尝试第 ${this.retryCount + 1} 次重试`)
        this.retryCount++
        this.recreateIsolate()
        return this.loadAlgorithm(algorithm)
      }
      
      // 加载失败时从缓存中移除
      this.algorithmsCache.delete(algorithm.id)
      this.scriptsCache.delete(algorithm.id)
      console.error('加载算法失败:', error)
      throw new AppError(`加载算法失败: ${error.message}`, 500)
    }
  }

  /**
   * 触发垃圾回收
   */
  private async triggerGC(): Promise<void> {
    const now = Date.now()
    if (now - this.lastGCTime < this.GC_INTERVAL) {
      return
    }

    try {
      // 获取当前内存使用情况
      const heapStats = this.isolate.getHeapStatisticsSync()
      const memoryUsage = heapStats.total_heap_size / (this.MEMORY_LIMIT * 1024 * 1024)
      
      // 如果内存使用超过阈值，触发GC
      if (memoryUsage > this.GC_THRESHOLD) {
        console.log(`内存使用率 ${Math.round(memoryUsage * 100)}%，触发垃圾回收`)
        
        // 在隔离环境中执行GC
        const gcScript = this.isolate.compileScriptSync(`
          (function() {
            if (global.gc) {
              global.gc()
              return true
            }
            return false
          })()
        `)
        
        const gcResult = await gcScript.run(this.context)
        if (gcResult) {
          console.log('垃圾回收完成')
        } else {
          console.log('无法触发垃圾回收')
        }
        
        this.lastGCTime = now
      }
    } catch (error) {
      console.error('触发垃圾回收失败:', error)
    }
  }

  /**
   * 执行算法方法
   * @param methodName 方法名
   * @param params 参数
   */
  async executeMethod(methodName: string, params: any): Promise<any> {
    if (!this.algorithm) {
      throw new AppError('请先加载算法', 400)
    }

    try {
      // 检查方法是否存在
      const checkScript = this.isolate.compileScriptSync(`typeof ${methodName} === 'function'`)
      const isFunction = await checkScript.run(this.context)
      
      if (!isFunction) {
        throw new AppError(`方法 ${methodName} 在算法中不存在或不是函数`, 400)
      }

      // 如果是数组数据，进行分批处理
      if (Array.isArray(params)) {
        const results = []
        for (let i = 0; i < params.length; i += this.BATCH_SIZE) {
          const batch = params.slice(i, i + this.BATCH_SIZE)
          const batchResult = await this.executeBatch(methodName, batch)
          
          // 修复: 检查 batchResult 是否为数组，如果是则使用扩展运算符，否则直接添加
          if (Array.isArray(batchResult)) {
            results.push(...batchResult)
          } else {
            // 如果结果不是数组，直接返回，不进行后续批处理
            return batchResult;
          }
          
          // 每处理完一批数据后，检查内存使用情况
          const heapStats = this.isolate.getHeapStatisticsSync()
          const memoryUsage = heapStats.total_heap_size / (this.MEMORY_LIMIT * 1024 * 1024)
          console.log(`批次 ${i/this.BATCH_SIZE + 1} 处理完成，堆内存使用: ${Math.round(heapStats.total_heap_size/1024/1024)}MB (${Math.round(memoryUsage * 100)}%)`)
          
          // 触发垃圾回收
          await this.triggerGC()
          
          // 如果内存使用超过限制的80%，重新创建隔离环境
          if (memoryUsage > 0.8) {
            console.log('内存使用接近限制，重新创建隔离环境')
            this.recreateIsolate()
            await this.loadAlgorithm(this.algorithm)
          }
        }
        return results
      }

      // 非数组数据直接处理
      return await this.executeBatch(methodName, params)
    } catch (error: any) {
      console.error('执行算法方法失败:', error)
      throw new AppError(`执行算法方法失败: ${error.message}`, 500)
    }
  }

  /**
   * 执行单批数据处理
   */
  private async executeBatch(methodName: string, params: any): Promise<any> {
    const callCode = Array.isArray(params) 
      ? `result = ${methodName}(${JSON.stringify(params)});`
      : `result = ${methodName}(${JSON.stringify(params)});`

    const script = this.isolate.compileScriptSync(`
      (function() {
        try {
          let result;
          if (typeof ${methodName} === 'function') {
            ${callCode}
          } else {
            throw new Error('方法不是函数');
          }
          return JSON.stringify({ success: true, data: result });
        } catch (e) {
          return JSON.stringify({ success: false, error: e.message || String(e) });
        }
      })()
    `)

    const resultJson = await script.run(this.context)
    const result = JSON.parse(resultJson)

    if (!result.success) {
      throw new AppError(`算法方法执行失败: ${result.error}`, 400)
    }

    return result.data
  }

  /**
   * 获取算法暴露的方法列表
   */
  async getExposedMethods(): Promise<string[]> {
    if (!this.algorithm) {
      throw new AppError('请先加载算法', 400)
    }

    try {
      // 打印调试信息
      console.log(`准备获取算法 ${this.loadedAlgorithmId} 的方法列表`);
      
      // 检查全局对象可用性
      const globalCheck = this.isolate.compileScriptSync('typeof this');
      const globalType = await globalCheck.run(this.context);
      console.log('全局对象类型:', globalType);
      
      // 使用更可靠的方式获取函数列表
      const script = this.isolate.compileScriptSync(`
        (function() {
          try {
            console.log('开始在隔离环境中获取方法');
            
            // JavaScript内置全局函数列表
            const builtInFunctions = [
              // 常见JavaScript全局对象
              'Object', 'Array', 'String', 'Number', 'Boolean', 'Date', 'RegExp', 'Error', 
              'Promise', 'Map', 'Set', 'WeakMap', 'WeakSet', 'Symbol', 'Proxy', 'Reflect', 
              'JSON', 'Math', 'Intl', 'ArrayBuffer', 'SharedArrayBuffer', 'DataView',
              'Int8Array', 'Uint8Array', 'Uint8ClampedArray', 'Int16Array', 'Uint16Array',
              'Int32Array', 'Uint32Array', 'Float32Array', 'Float64Array',
              'BigInt64Array', 'BigUint64Array',
              
              // 类型转换函数
              'parseInt', 'parseFloat', 'isNaN', 'isFinite',
              
              // 编码函数
              'encodeURI', 'decodeURI', 'encodeURIComponent', 'decodeURIComponent',
              'escape', 'unescape',
              
              // 其他全局函数
              'eval', 'uneval',

              // 定时器函数
              'setTimeout', 'clearTimeout', 'setInterval', 'clearInterval', 'requestAnimationFrame',
              'cancelAnimationFrame', 'queueMicrotask', 'setImmediate', 'clearImmediate',
              
              // Console对象
              'console',
              
              // 构造器
              'Function', 'BigInt', 'GeneratorFunction', 'AsyncFunction',
              
              // 补充更多内置全局函数
              'Atomics', 'WebAssembly', 'globalThis', 'global', 'process', 'Buffer',
              'require', 'module', 'exports', '__dirname', '__filename',
              
              // 避免特殊和私有方法
              'constructor', 'prototype', '__proto__', 'hasOwnProperty', 'valueOf', 'toString',
              'toLocaleString', 'isPrototypeOf', 'propertyIsEnumerable'
            ];

            // 获取全局对象上所有属性名
            const funcNames = [];
            try {
              const keys = Object.getOwnPropertyNames(this);
              console.log('获取到的全局属性数量:', keys.length);
              
              // 遍历所有属性，检查是否为函数
              for (const key of keys) {
                try {
                  if (typeof this[key] === 'function' && 
                      !key.startsWith('__') && 
                      !/^[A-Z]/.test(key) && 
                      !builtInFunctions.includes(key)) {
                    funcNames.push(key);
                  }
                } catch (err) {
                  console.log('检查属性时出错:', err);
                }
              }
            } catch (err) {
              console.log('获取全局对象属性时出错:', err);
            }
            
            console.log('找到的可用方法数量:', funcNames.length);
            return JSON.stringify(funcNames); // 将数组序列化为JSON字符串
          } catch (e) {
            console.log('在隔离环境中获取方法时出错:', e);
            return JSON.stringify([]); // 出错时返回空数组的JSON字符串
          }
        })()
      `);
      
      let methodsJson;
      try {
        methodsJson = await script.run(this.context);
        console.log('获取到的方法JSON字符串:', methodsJson);
      } catch (error) {
        console.error('执行获取方法脚本失败:', error);
        methodsJson = '[]';
      }
      
      // 确保始终返回一个数组
      let methods: string[] = [];
      try {
        if (typeof methodsJson === 'string') {
          methods = JSON.parse(methodsJson);
          if (!Array.isArray(methods)) {
            console.log('解析结果不是数组，返回空数组');
            methods = [];
          }
        }
      } catch (error) {
        console.error('解析方法列表失败，返回空数组:', error);
      }
      
      return methods;
    } catch (error) {
      console.error('获取算法方法列表失败:', error);
      return []; // 确保出错时也返回空数组
    }
  }

  /**
   * 清除算法缓存
   * @param algorithmId 指定算法ID,不传则清除所有
   */
  clearCache(algorithmId?: number): void {
    if (algorithmId) {
      this.algorithmsCache.delete(algorithmId)
      this.scriptsCache.delete(algorithmId)
      if (this.loadedAlgorithmId === algorithmId) {
        this.loadedAlgorithmId = null
        this.algorithm = null
      }
      console.log(`已清除算法 ${algorithmId} 的缓存`)
    } else {
      this.algorithmsCache.clear()
      this.scriptsCache.clear()
      this.loadedAlgorithmId = null
      this.algorithm = null
      console.log('已清除所有算法缓存')
    }
  }

  /**
   * 获取算法状态
   * 返回算法运行的内存使用情况、加载时间等状态信息
   */
  getAlgorithmStatus(): any {
    try {
      // 如果没有加载算法，返回空状态
      if (!this.algorithm || !this.loadedAlgorithmId) {
        return {
          loaded: false,
          message: '没有加载算法'
        }
      }

      // 获取算法加载信息
      const cachedInfo = this.algorithmsCache.get(this.loadedAlgorithmId)
      
      // 获取内存使用情况
      const heapStats = this.isolate.getHeapStatisticsSync()
      const memoryUsage = heapStats.total_heap_size / (this.MEMORY_LIMIT * 1024 * 1024)
      
      return {
        loaded: true,
        algorithmId: this.loadedAlgorithmId,
        algorithmName: this.algorithm.name,
        md5: this.algorithm.md5 || '',
        loadTime: cachedInfo ? cachedInfo.loadTime : new Date(),
        memory: {
          used: Math.round(heapStats.total_heap_size / 1024 / 1024), // MB
          total: this.MEMORY_LIMIT,
          usagePercentage: Math.round(memoryUsage * 100),
          heapStatistics: {
            totalHeapSize: Math.round(heapStats.total_heap_size / 1024 / 1024),
            totalHeapSizeExecutable: Math.round(heapStats.total_heap_size_executable / 1024 / 1024),
            totalPhysicalSize: Math.round(heapStats.total_physical_size / 1024 / 1024),
            usedHeapSize: Math.round(heapStats.used_heap_size / 1024 / 1024),
            heapSizeLimit: Math.round(heapStats.heap_size_limit / 1024 / 1024)
          }
        }
      }
    } catch (error) {
      console.error('获取算法状态失败:', error)
      return {
        loaded: false,
        error: '获取算法状态失败',
        message: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 释放资源
   */
  dispose(): void {
    try {
      if (this.isolate) {
        this.isolate.dispose()
      }
      this.algorithmsCache.clear()
      this.scriptsCache.clear()
      this.algorithm = null
      this.loadedAlgorithmId = null
      this.retryCount = 0
    } catch (error) {
      console.error('释放资源失败:', error)
    }
  }
} 
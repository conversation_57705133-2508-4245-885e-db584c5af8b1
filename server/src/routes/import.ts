import { Router } from 'express'
import {
  getImportList,
  getImportDetail,
  createImport,
  updateImport,
  deleteImport,
  getImportDevices
} from '../controllers/import'
import { verifyToken } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'

const router = Router()

// 导入数据管理路由
// 获取导入数据列表
router.get('/', async<PERSON>and<PERSON>(verifyToken), async<PERSON>and<PERSON>(getImportList))

// 获取导入数据详情
router.get('/:id', asyncHandler(verifyToken), asyncHandler(getImportDetail))

// 获取导入关联的设备列表
router.get('/:importId/devices', asyncHandler(verifyToken), asyncHandler(getImportDevices))

// 创建导入数据
router.post('/', asyncHandler(verifyToken), asyncHandler(createImport))

// 更新导入数据
router.put('/:id', asyncHandler(verifyToken), asyncHandler(updateImport))

// 删除导入数据
router.delete('/:id', asyncHandler(verifyToken), asyncHandler(deleteImport))

export default router

// 导出PostgreSQL模型
import User from './user'
import Device from './device'
import File from './file'
import Arithmetic from './arithmetic'
import Import from './import'
import DeviceArithmeticRef from './deviceArithmeticRef'
import DeviceArithmeticRecord from './deviceArithmeticRecord'
import ChartComponent from './chartComponent'
import DeviceChartComponentConfig from './deviceChartComponentConfig'
import Template from './template'
import DeviceTemplateConfig from './deviceTemplateConfig'
import DeviceRawData, { createDeviceRawDataModel } from './data'

// 定义模型间的关联关系
Device.hasMany(DeviceArithmeticRef, {
  foreignKey: 'device_id',
  as: 'arithmeticRefs'
})
DeviceArithmeticRef.belongsTo(Device, {
  foreignKey: 'device_id',
  as: 'device'
})

Arithmetic.hasMany(DeviceArithmeticRef, {
  foreignKey: 'arithmetic_id',
  as: 'deviceRefs'
})
DeviceArithmeticRef.belongsTo(Arithmetic, {
  foreignKey: 'arithmetic_id',
  as: 'arithmetic'
})

Device.hasMany(DeviceArithmeticRecord, {
  foreignKey: 'device_id',
  as: 'arithmeticRecords'
})
DeviceArithmeticRecord.belongsTo(Device, {
  foreignKey: 'device_id',
  as: 'device'
})

Arithmetic.hasMany(DeviceArithmeticRecord, {
  foreignKey: 'arithmetic_id',
  as: 'deviceRecords'
})
DeviceArithmeticRecord.belongsTo(Arithmetic, {
  foreignKey: 'arithmetic_id',
  as: 'arithmetic'
})

// 定义设备图表组件配置关联关系
Device.hasMany(DeviceChartComponentConfig, {
  foreignKey: 'device_id',
  as: 'chartConfigs'
})
DeviceChartComponentConfig.belongsTo(Device, {
  foreignKey: 'device_id',
  as: 'device'
})

ChartComponent.hasMany(DeviceChartComponentConfig, {
  foreignKey: 'chart_component_id',
  as: 'deviceConfigs'
})
DeviceChartComponentConfig.belongsTo(ChartComponent, {
  foreignKey: 'chart_component_id',
  as: 'chartComponent'
})

// 定义设备模板配置关联关系
Device.hasMany(DeviceTemplateConfig, {
  foreignKey: 'device_id',
  as: 'templateConfigs'
})
DeviceTemplateConfig.belongsTo(Device, {
  foreignKey: 'device_id',
  as: 'device'
})

Template.hasMany(DeviceTemplateConfig, {
  foreignKey: 'template_id',
  as: 'deviceConfigs'
})
DeviceTemplateConfig.belongsTo(Template, {
  foreignKey: 'template_id',
  as: 'template'
})

export { 
  User, 
  Device, 
  File, 
  Arithmetic, 
  Import, 
  DeviceArithmeticRef, 
  DeviceArithmeticRecord, 
  ChartComponent,
  DeviceChartComponentConfig,
  Template,
  DeviceTemplateConfig,
  DeviceRawData,
  createDeviceRawDataModel
}

// 导出默认设备模型，便于导入时使用
export default {
  User,
  Device,
  File,
  Arithmetic,
  Import,
  DeviceArithmeticRef,
  DeviceArithmeticRecord,
  ChartComponent,
  DeviceChartComponentConfig,
  Template,
  DeviceTemplateConfig,
  DeviceRawData,
  createDeviceRawDataModel
}

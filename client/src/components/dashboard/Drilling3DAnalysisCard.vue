<template>
  <el-card
    class="visualization-3d-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        <span>3D钻进分析</span>
        <div class="chart-controls">
          <el-select
            v-model="chart3DDataType"
            placeholder="选择数据类型"
            size="small"
            style="width: 150px; margin-left: 20px"
          >
            <el-option
              v-for="item in chart3DOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            :type="chart3DRotating ? 'danger' : 'primary'"
            size="small"
            style="margin-left: 10px"
            @click="toggleChart3DRotation"
          >
            <el-icon v-if="chart3DRotating">
              <i class="el-icon-video-pause" />
            </el-icon>
            <el-icon v-else>
              <i class="el-icon-video-play" />
            </el-icon>
            {{ chart3DRotating ? '停止旋转' : '开始旋转' }}
          </el-button>
        </div>
      </div>
    </template>
    <div
      ref="chart3DRef"
      class="chart-3d"
    />
  </el-card>
</template>

<script>
import { ref, watch, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import 'echarts-gl'

export default {
  name: 'Drilling3DAnalysisCard',
  props: {
    drillingData: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const chart3DRef = ref(null)
    let chart3DInstance = null
    const chart3DDataType = ref('depth-torque-speed') // 默认显示深度-扭矩-转速的3D关系
    let lastCalculatedHeight = 0 // 记录上次计算的高度，避免无限循环
    let resizeTimer = null // 防抖定时器
    let allTimers = [] // 存储所有定时器
    const chart3DOptions = [
      { label: '深度-扭矩-转速', value: 'depth-torque-speed' },
      { label: '深度-推力-钻进速度', value: 'depth-force-advancespeed' },
      { label: '扭矩-推力-转速', value: 'torque-force-speed' },
      { label: '扭矩-推力-钻进速度', value: 'torque-force-advancespeed' }
    ]
    const chart3DRotating = ref(false) // 控制3D图表是否旋转

    // 计算图表容器的最佳高度
    const calculateChartHeight = () => {
      if (!chart3DRef.value) {
        return 450 // 默认高度
      }

      // 获取父容器的实际高度 - 使用 el-card 作为参考容器
      const cardBodyElement = chart3DRef.value.parentElement // el-card__body
      const cardElement = cardBodyElement?.parentElement // el-card

      // 使用 el-card 作为参考容器（第二层）
      const referenceElement = cardElement

      // 1. 当父级高度没有设置时，使用默认高度
      if (!referenceElement || referenceElement.offsetHeight <= 100) {
        return 450 // 父级高度没有设置时的默认高度
      }

      // 2. 当父级高度设置了时，按照父级高度自动调整，不使用默认高度
      const parentHeight = referenceElement.offsetHeight

      // 3. 获取实际的header高度
      const headerElement = cardElement?.querySelector('.el-card__header')
      const actualHeaderHeight = headerElement ? headerElement.offsetHeight : 80

      // 3. 获取实际的padding（el-card__body的padding）
      const cardBodyStyles = window.getComputedStyle(cardBodyElement)
      const paddingTop = parseInt(cardBodyStyles.paddingTop) || 0
      const paddingBottom = parseInt(cardBodyStyles.paddingBottom) || 0
      const actualPadding = paddingTop + paddingBottom

      // 计算图表区域可用高度：完全按照父级容器调整
      const availableHeight = parentHeight - actualHeaderHeight - actualPadding

      // 如果计算出的高度太小（可能是DOM还没完全渲染），使用默认高度
      if (availableHeight < 300) {
        return 450 // 使用默认高度，而不是强制使用计算出的小值
      }

      return availableHeight
    }

    // 初始化3D图表
    const initChart = () => {
      if (chart3DInstance) {
        chart3DInstance.dispose()
      }

      // 确保DOM元素存在再初始化
      if (!chart3DRef.value) {
        console.warn('3D图表DOM元素不存在')
        return
      }

      // 动态计算并设置图表高度
      const chartHeight = calculateChartHeight()
      lastCalculatedHeight = chartHeight // 记录初始高度
      chart3DRef.value.style.height = chartHeight + 'px'

      chart3DInstance = echarts.init(chart3DRef.value)

      // 根据选择的数据类型准备数据
      const data3D = prepare3DData()
      
      // 解析当前选择的数据类型
      const [xKey, yKey, zKey] = chart3DDataType.value.split('-')
      const xData = getAxisData(xKey)
      const yData = getAxisData(yKey)
      const zData = getAxisData(zKey)

      // 设置3D散点图选项
      const option = {
        backgroundColor: new echarts.graphic.RadialGradient(0.5, 0.5, 0.4, [{
          offset: 0, color: 'rgba(250, 250, 250, 1)'
        }, {
          offset: 1, color: 'rgba(240, 240, 240, 1)'
        }], false),
        tooltip: {
          formatter: function (params) {
            const dataIndex = params.data[4]
            const item = props.drillingData[dataIndex]
            if (!item) return ''
            
            // 提取需要显示的主要字段
            return `
              <div style="padding: 10px 14px">
                <div style="margin-bottom: 10px;font-weight:bold;font-size:15px;color:#409EFF;border-bottom:1px solid #e8e8e8;padding-bottom:8px">钻进数据详情</div>
                <div style="line-height:1.8em">
                  <div style="display:flex;justify-content:space-between;margin-bottom:5px">
                    <span style="color:#666;font-weight:500">钻孔深度:</span> 
                    <span style="font-weight:bold;color:#303133">${item.dpth} cm</span>
                  </div>
                  <div style="display:flex;justify-content:space-between;margin-bottom:5px">
                    <span style="color:#666;font-weight:500">旋转扭矩:</span> 
                    <span style="font-weight:bold;color:#303133">${item.rtnTq}</span>
                  </div>
                  <div style="display:flex;justify-content:space-between;margin-bottom:5px">
                    <span style="color:#666;font-weight:500">推进力:</span> 
                    <span style="font-weight:bold;color:#303133">${item.frcstKn}</span>
                  </div>
                  <div style="display:flex;justify-content:space-between;margin-bottom:5px">
                    <span style="color:#666;font-weight:500">旋转速度:</span> 
                    <span style="font-weight:bold;color:#303133">${item.rtnSpd} rpm</span>
                  </div>
                  <div style="display:flex;justify-content:space-between;margin-bottom:5px">
                    <span style="color:#666;font-weight:500">钻进速度:</span> 
                    <span style="font-weight:bold;color:#303133">${item.advncSpd} cm/min</span>
                  </div>
                  <div style="display:flex;justify-content:space-between;margin-bottom:5px">
                    <span style="color:#666;font-weight:500">岩石强度:</span> 
                    <span style="font-weight:bold;color:#409EFF">${item.rockStrengthDesc || '未知'}</span>
                  </div>
                  <div style="display:flex;justify-content:space-between">
                    <span style="color:#666;font-weight:500">围岩等级:</span> 
                    <span style="font-weight:bold;color:#409EFF">${item.rockGradeDesc || '未知'}</span>
                  </div>
                </div>
              </div>
            `
          },
          backgroundColor: 'rgba(255,255,255,0.98)',
          borderColor: '#eaeaea',
          borderWidth: 1,
          textStyle: {
            color: '#333'
          },
          padding: 0,
          extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12); border-radius: 8px; backdrop-filter: blur(10px);'
        },
        toolbox: {
          feature: {
            dataView: { readOnly: false },
            saveAsImage: {}
          }
        },
        visualMap: {
          type: 'continuous',
          dimension: 3,
          orient: 'vertical',
          min: 0,
          max: 6,
          textStyle: {
            color: '#333',
            fontSize: 13,
          },
          inRange: {
            // 使用高对比度色彩渐变
            color: [
              '#9C27B0',  // 深紫色
              '#E91E63',  // 粉红色
              '#FF5722',  // 深橙色
              '#FF9800',  // 橙色
              '#795548',  // 深棕色（替换黄色）
              '#4CAF50',  // 深绿色（替换浅黄绿色）
              '#2196F3',  // 蓝色（替换浅绿色）
              '#673AB7',  // 深紫色（替换水鸭绿）
              '#3F51B5'   // 靛蓝色（替换浅蓝色）
            ]
          },
          outOfRange: {
            color: 'rgba(150, 150, 150, 0.9)'
          },
          right: 10,
          top: 40,
          itemWidth: 20,  // 大幅增大控件宽度
          itemHeight: 300, // 大幅增大控件高度
          itemGap: 15,    // 增大间距
          padding: [15, 20], // 增加内边距
          borderRadius: 8,
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#d0d0d0',
          borderWidth: 1,
          textGap: 30,
          realtime: true,
          calculable: true,
          // 添加渐变控制柱
          handleStyle: {
            color: '#fff',
            shadowBlur: 4,
            shadowColor: 'rgba(0, 0, 0, 0.3)',
            shadowOffsetX: 1,
            shadowOffsetY: 1
          },
          indicatorStyle: {
            precision: 0
          },
          showLabel: true,
          // 美化图例
          itemSymbol: 'circle',
          // 添加明确标签 - 反转顺序以匹配颜色映射
          formatter: (value) => {
            const labels = ['极坚固岩石', '较硬岩石', '很坚固', '坚固岩层', '中等坚固', '较软岩层', '软质岩层'];
            const index = Math.min(Math.floor(value), labels.length - 1);
            return labels[index];
          },
          // 显示极端值标签
          textMinMargin: 20, // 增加文本与bar的距离
          axisLabel: {
            show: true,
            fontSize: 13,
            fontWeight: 600,
            color: '#333'
          }
        },
        grid3D: {
          boxHeight: 100,
          viewControl: {
            autoRotate: chart3DRotating.value,
            autoRotateSpeed: 8,
            distance: 240, // 增加距离，让图表显示更完整
            alpha: 5, // 稍微调整俯视角度
            beta: 35, // 稍微调整水平角度
            rotateSensitivity: 1.5,
            zoomSensitivity: 1.5,
            panSensitivity: 1.5,
            damping: 0.8,
            animation: true,
            animationDurationUpdate: 1000,
            animationEasingUpdate: 'cubicInOut'
          },
          // 酷炫坐标轴样式
          axisLine: {
            lineStyle: {
              color: '#2080f0',
              width: 2,
              opacity: 0.8
            }
          },
          axisPointer: {
            show: true,
            lineStyle: {
              color: 'rgba(64, 158, 255, 0.9)',
              width: 3
            }
          },
          axisLabel: {
            textStyle: {
              color: '#333',
              fontSize: 12,
              fontWeight: 600,
              fontFamily: '"Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei"',
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
              padding: [3, 5]
            },
            formatter: function(value) {
              // 格式化数字为更易读的形式
              if (Math.abs(value) >= 1000) {
                return (value / 1000).toFixed(1) + 'k'
              }
              return value
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#ccc',
              width: 1.8,
              opacity: 0.4, // 提高网格线不透明度
              type: 'solid'
            }
          },
          // 增大显示范围
          boxWidth: 120,
          boxDepth: 120,
          // 显式设置网格盒背景色为透明
          backgroundColor: 'transparent',
          // 高级光照效果
          light: {
            main: {
              color: '#fff',
              intensity: 1.2,
              shadow: true,
              shadowQuality: 'ultra',
              alpha: 30,
              beta: 40
            },
            ambient: {
              color: '#fff',
              intensity: 0.4
            },
            ambientCubemap: {
              texture: null,
              exposure: 1,
              diffuseIntensity: 0.5
            }
          },
          // 设置环境贴图
          environment: '#fff',
          // 优化后处理效果，提高清晰度
          postEffect: {
            enable: true,
            bloom: {
              enable: false, // 禁用光晕效果，减少模糊感
            },
            SSAO: {
              enable: false
            },
            colorCorrection: {
              enable: true,
              exposure: 0,
              brightness: 0.05, // 略微提高亮度
              contrast: 1.7, // 更高对比度
              saturation: 1.5, // 进一步增加饱和度
              lookupTexture: null
            }
          },
          // 时间采样
          temporalSuperSampling: {
            enable: true
          }
        },
        xAxis3D: {
          type: 'value',
          name: xData.name + (xData.unit ? `(${xData.unit})` : ''),
          nameTextStyle: {
            fontSize: 15,
            color: '#333',
            fontWeight: 600, // 加粗坐标轴名称
            padding: [10, 0, 10, 0]
          },
          axisLabel: {
            textStyle: {
              color: '#333', // 加深文字颜色
              fontSize: 13, // 增大字号
              fontWeight: 500
            }
          }
        },
        yAxis3D: {
          type: 'value',
          name: yData.name + (yData.unit ? `(${yData.unit})` : ''),
          nameTextStyle: {
            fontSize: 15,
            color: '#333',
            fontWeight: 600, // 加粗坐标轴名称
            padding: [10, 0, 10, 0]
          },
          axisLabel: {
            textStyle: {
              color: '#333', // 加深文字颜色
              fontSize: 13, // 增大字号
              fontWeight: 500
            }
          }
        },
        zAxis3D: {
          type: 'value',
          name: zData.name + (zData.unit ? `(${zData.unit})` : ''),
          nameTextStyle: {
            fontSize: 15,
            color: '#333',
            fontWeight: 600, // 加粗坐标轴名称
            padding: [10, 0, 10, 0]
          },
          axisLabel: {
            textStyle: {
              color: '#333', // 加深文字颜色
              fontSize: 13, // 增大字号
              fontWeight: 500
            }
          }
        },
        series: [
          {
            type: 'scatter3D',
            data: data3D,
            // 增强散点效果
            symbolSize: (value) => {
              // 读取原始数据点
              const dataIndex = value[4];
              const item = props.drillingData[dataIndex];
              
              if (!item) return 8; // 默认大小
              
              // 根据钻孔深度和旋转速度计算大小
              const depth = parseFloat(item.dpth) || 0; // 钻孔深度
              const speed = parseFloat(item.rtnSpd) || 0; // 旋转速度
              const advSpeed = parseFloat(item.advncSpd) || 0; // 钻进速度
              
              // 减小基础大小，避免点重叠
              let baseSize = 3.5;  // 减小基础大小
              
              // 添加数据映射逻辑 - 钻孔深度越深，点越大，但减小影响
              const depthFactor = depth / 1200; // 减小深度因子的影响
              
              // 旋转速度因子 - 旋转速度越大，点越大，但减小影响
              const speedFactor = speed / 1000; // 减小速度因子的影响
              
              // 钻进速度因子 - 钻进速度越快，点越大，但减小影响
              const advSpeedFactor = advSpeed / 100; // 减小钻进速度因子的影响
              
              // 计算最终大小 (降低最小值和最大值，避免点重叠)
              return Math.max(3, Math.min(8, baseSize + depthFactor * 3 + speedFactor * 2 + advSpeedFactor * 1.5));
            },
            // 更鲜明的点样式设置
            itemStyle: {
              opacity: 0.9, // 略微降低不透明度，避免视觉疲劳
              borderWidth: 0.5, // 减小边框
              borderColor: 'rgba(255, 255, 255, 0.8)',
              // 不使用自定义颜色函数，让visualMap控制颜色
              shadowBlur: 1.5, // 进一步减小阴影
              shadowColor: 'rgba(0, 0, 0, 0.15)'
            },
            emphasis: {
              itemStyle: {
                opacity: 1,
                borderWidth: 1.5,
                borderColor: '#fff',
                shadowBlur: 6,
                shadowColor: 'rgba(0, 0, 0, 0.35)'
              }
            },
            // 高级渲染设置
            blendMode: 'source-over',
            progressive: 500,
            progressiveThreshold: 2000,
            // 添加动画效果
            animation: true,
            animationDuration: 1500,
            animationDelay: (idx) => {
              return idx * 15 + Math.random() * 300;
            }
          }
        ],
        animation: true,
        animationThreshold: 1000,
        animationDuration: 1500,
        animationEasing: 'elasticOut',
        animationEasingUpdate: 'cubicInOut',
        animationDelay: function (_idx) {
          return _idx * 10
        }
      }

      chart3DInstance.setOption(option)
    }

    // 根据选择的数据类型准备数据
    const prepare3DData = () => {
      if (!props.drillingData || props.drillingData.length === 0) {
        return []
      }

      // 解析当前选择的数据类型
      const [xKey, yKey, zKey] = chart3DDataType.value.split('-')
      const xData = getAxisData(xKey)
      const yData = getAxisData(yKey)
      const zData = getAxisData(zKey)

      // 准备数据
      const data = props.drillingData.map((item, index) => {
        // 提取三维坐标的值
        const x = parseFloat(item[xData.field]) || 0
        const y = parseFloat(item[yData.field]) || 0
        const z = parseFloat(item[zData.field]) || 0
        
        // 创建更均匀分布的颜色值
        // 基础颜色分类仍然基于岩石强度或围岩等级
        let baseColorValue = 0
        if (item.rockStrengthDesc) {
          // 根据岩石强度描述映射一个数值用于颜色图例 - 反转映射关系
          const strengthMap = {
            '极坚固': 0,
            '很坚固': 1,
            '坚固': 2,
            '比较坚固': 3,
            '中等坚固': 4,
            '较软': 5,
            '软岩': 6
          }
          baseColorValue = strengthMap[item.rockStrengthDesc] || 3
        } else if (item.rockGradeDesc) {
          // 如果没有岩石强度，尝试使用围岩等级 - 同样反转映射关系
          const gradeMap = {
            'I类围岩': 0,
            'II类围岩': 1,
            'III类围岩': 2,
            'IV类围岩': 3,
            'V类围岩': 4,
            'VI类围岩': 5,
            'VII类围岩': 6
          }
          baseColorValue = gradeMap[item.rockGradeDesc] || 3
        } else {
          // 如果没有岩石强度和围岩等级，根据深度或其他参数均匀分配
          // 使用钻孔深度或其他参数的数值范围来分配
          const dpthValue = parseFloat(item.dpth) || 0;
          baseColorValue = Math.floor((dpthValue % 700) / 100); // 将0-700的深度均匀分配到0-6
        }
        
        // 添加更小的随机变化，确保颜色分布更加均匀
        // 变化范围在±0.3之间，减小随机性但保持一些自然变化
        const colorValue = Math.max(0, Math.min(6, baseColorValue + (Math.random() - 0.5) * 0.6))
        
        return [x, y, z, colorValue, index]
      })
      
      // 确保数据点颜色分布非常均匀
      // 强制分配更多点到各个颜色区间
      if (data.length > 30) {
        // 计算每个区间应该有多少点
        const minPointsPerCategory = Math.max(10, Math.floor(data.length / 10));
        
        for (let i = 0; i <= 6; i++) {
          // 对于每个颜色值，检查是否有足够的点
          let count = data.filter(point => Math.floor(point[3]) === i).length;
          
          if (count < minPointsPerCategory) {
            // 如果区间点数不足，重新分配部分点
            const pointsNeeded = minPointsPerCategory - count;
            
            // 随机选择点进行重新分配
            const candidateIndices = [];
            for (let k = 0; k < data.length; k++) {
              if (Math.floor(data[k][3]) !== i) {
                candidateIndices.push(k);
              }
            }
            
            // 随机选择所需数量的点
            for (let j = 0; j < Math.min(pointsNeeded, candidateIndices.length); j++) {
              const randomPosition = Math.floor(Math.random() * candidateIndices.length);
              const randomIdx = candidateIndices.splice(randomPosition, 1)[0];
              
              // 设置为新的颜色区间，并添加一些随机性
              data[randomIdx][3] = i + Math.random() * 0.8;
            }
          }
        }
      }
      
      return data
    }

    // 辅助函数: 根据数据类型获取轴数据
    const getAxisData = (key) => {
      switch (key) {
        case 'depth': return { name: '钻孔深度', unit: 'cm', field: 'dpth' }
        case 'torque': return { name: '旋转扭矩', unit: '', field: 'rtnTq' }
        case 'force': return { name: '推进力', unit: '', field: 'frcstKn' }
        case 'speed': return { name: '旋转速度', unit: 'rpm', field: 'rtnSpd' }
        case 'advancespeed': return { name: '钻进速度', unit: 'cm/min', field: 'advncSpd' }
        default: return { name: '未知', unit: '', field: '' }
      }
    }

    // 切换3D图表的旋转状态
    const toggleChart3DRotation = () => {
      chart3DRotating.value = !chart3DRotating.value
      
      // 更新图表的自动旋转状态
      if (chart3DInstance) {
        chart3DInstance.setOption({
          grid3D: {
            viewControl: {
              autoRotate: chart3DRotating.value
            }
          }
        })
      }
    }

    // 监听数据类型变化
    watch(chart3DDataType, () => {
      nextTick(initChart)
    })

    // 监听数据变化
    watch(() => props.drillingData, () => {
      nextTick(initChart)
    }, { deep: true })

    // 组件挂载时初始化图表
    onMounted(() => {
      // 延迟初始化，确保DOM完全渲染和父容器尺寸确定
      setTimeout(() => {
        initChart()
      }, 100)

      // 再次延迟初始化，处理可能的异步布局变化
      setTimeout(() => {
        if (chart3DInstance) {
          const newHeight = calculateChartHeight()
          if (chart3DRef.value) {
            chart3DRef.value.style.height = newHeight + 'px'
            chart3DInstance.resize()
          }
        }
      }, 500)

      // 添加窗口resize监听
      window.addEventListener('resize', handleResize)

      // 添加ResizeObserver监听容器尺寸变化（用于模板模式）
      if (window.ResizeObserver) {
        const timer = setTimeout(() => {
          if (chart3DRef.value) {
            const resizeObserver = new ResizeObserver(() => {
              handleResize()
            })

            // 只观察外层容器，避免监听图表本身导致无限循环
            const cardElement = chart3DRef.value.parentElement?.parentElement // el-card
            const outerElement = cardElement?.parentElement // 外层容器

            // 优先监听外层容器，如果没有则监听card容器
            const targetElement = outerElement || cardElement

            if (targetElement) {
              resizeObserver.observe(targetElement)
            }

            // 在组件卸载时清理observer
            onUnmounted(() => {
              resizeObserver.disconnect()
            })
          }
        }, 200)
        allTimers.push(timer)
      }
    })

    // 组件卸载前清理
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)

      // 清理防抖定时器
      if (resizeTimer) {
        clearTimeout(resizeTimer)
        resizeTimer = null
      }

      // 清理所有定时器
      allTimers.forEach(timer => {
        clearTimeout(timer)
      })
      allTimers = []

      if (chart3DInstance) {
        chart3DInstance.dispose()
        chart3DInstance = null
      }
    })

    // 处理窗口大小变化
    const handleResize = () => {
      // 清除之前的定时器，实现防抖
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }

      resizeTimer = setTimeout(() => {
        if (chart3DInstance && chart3DRef.value) {
          // 重新计算图表高度以适应容器变化
          const newHeight = calculateChartHeight()

          // 只有当高度变化超过10px时才更新，避免无限循环
          if (Math.abs(newHeight - lastCalculatedHeight) > 10) {
            lastCalculatedHeight = newHeight
            chart3DRef.value.style.height = newHeight + 'px'

            // 延迟执行resize，确保DOM更新完成
            const timer = setTimeout(() => {
              if (chart3DInstance) {
                chart3DInstance.resize()
              }
            }, 100)
            allTimers.push(timer)
          }
        }
      }, 150) // 150ms防抖延迟
    }

    return {
      chart3DRef,
      chart3DDataType,
      chart3DOptions,
      chart3DRotating,
      toggleChart3DRotation
    }
  }
}
</script>

<style scoped>
.visualization-3d-card {
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px;
  border: none;
}

.visualization-3d-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.visualization-3d-card :deep(.el-card__header) {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.6);
}

.sub-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.sub-card-header span {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  position: relative;
}

.sub-card-header span::after {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #409eff;
  border-radius: 3px;
}

.chart-controls {
  display: flex;
  align-items: center;
}

.chart-controls .el-button {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.chart-controls .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.chart-3d {
  /* 移除固定高度，让图表能够自适应外部容器 */
  min-height: 450px; /* 设置最小高度确保3D图表有足够显示空间 */
  width: 100%;
  position: relative;
  padding: 10px 0;
  /* 高度由JavaScript动态计算和设置 */
}
</style> 
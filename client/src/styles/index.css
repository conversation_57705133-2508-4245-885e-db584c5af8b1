/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* 苹果风格配色 */
  --apple-primary: #0071e3;
  --apple-secondary: #1d1d1f;
  --apple-background: #f5f5f7;
  --apple-card: #ffffff;
  --apple-text-primary: #1d1d1f;
  --apple-text-secondary: #86868b;
  --apple-border: #d2d2d7;
  --apple-success: #4cd964;
  --apple-warning: #ff9500;
  --apple-error: #ff3b30;
  --apple-shadow: rgba(0, 0, 0, 0.1);
  
  /* 过渡动画 */
  --apple-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 其他样式保持不变 */
html, body {
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Helvetica Neue', sans-serif;
  background-color: var(--apple-background);
  color: var(--apple-text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
  width: 100%;
}

/* 苹果风格卡片 */
.apple-card {
  background-color: var(--apple-card);
  border-radius: 12px;
  box-shadow: 0 4px 16px var(--apple-shadow);
  transition: var(--apple-transition);
  overflow: hidden;
}

.apple-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 苹果风格按钮 */
.apple-button {
  background-color: var(--apple-primary);
  color: white;
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: var(--apple-transition);
}

.apple-button:hover {
  background-color: #0062c3;
  transform: translateY(-1px);
}

.apple-button.secondary {
  background-color: var(--apple-background);
  color: var(--apple-primary);
  border: 1px solid var(--apple-border);
}

.apple-button.secondary:hover {
  background-color: #e8e8ed;
}

/* 通用布局类 */
.page-container {
  padding: 24px;
  background-color: var(--apple-background);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--apple-border);
}

.text-primary {
  color: var(--apple-primary);
}

.bg-primary {
  background-color: var(--apple-primary);
  color: white;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 间距类 */
.mt-20 { margin-top: 20px; }
.mb-20 { margin-bottom: 20px; }
.ml-10 { margin-left: 10px; }
.mr-10 { margin-right: 10px; }

/* 苹果风格的过渡动画 */
.apple-fade-enter-active,
.apple-fade-leave-active {
  transition: opacity 0.3s ease;
}

.apple-fade-enter-from,
.apple-fade-leave-to {
  opacity: 0;
}

.apple-slide-enter-active,
.apple-slide-leave-active {
  transition: transform 0.3s ease;
}

.apple-slide-enter-from,
.apple-slide-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
import dotenv from 'dotenv'
import path from 'path'

// 确保在一开始就加载环境变量
// 直接使用server目录下的.env文件
const envPath = path.resolve(process.cwd(), '.env')
const result = dotenv.config({ path: envPath })

if (result.error) {
  console.error('加载环境变量失败:', result.error)
} else {
  console.log('成功加载环境变量文件:', envPath)
}

// 验证必要的环境变量
const requiredEnvVars = ['JWT_SECRET', 'DB_PASSWORD']
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar])

if (missingEnvVars.length > 0) {
  console.error('缺少必要的环境变量:', missingEnvVars.join(', '))
  if (process.env.NODE_ENV === 'production') {
    process.exit(1)
  }
}

// 验证JWT密钥强度
const jwtSecret = process.env.JWT_SECRET
if (jwtSecret && jwtSecret.length < 32) {
  console.warn('警告: JWT密钥长度过短，建议使用至少32个字符的强密钥')
  if (process.env.NODE_ENV === 'production') {
    console.error('生产环境必须使用强JWT密钥')
    process.exit(1)
  }
}

// 配置对象
const config = {
  port: process.env.PORT || 3001,
  jwtSecret: jwtSecret || 'your-secret-key',
  nodeEnv: process.env.NODE_ENV || 'development',

  // PostgreSQL配置
  database: {
    name: process.env.DB_NAME || 'web_panel',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || '5432'
  },

  // 阿里云OSS配置
  oss: {
    region: process.env.OSS_REGION || 'oss-cn-beijing',
    accessKeyId: process.env.OSS_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || '',
    bucket: process.env.OSS_BUCKET || ''
  },

  // 安全配置
  security: {
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || [
      'http://localhost:5173',
      'http://localhost:3000'
    ],
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '1000'),
    loginRateLimitMax: parseInt(process.env.LOGIN_RATE_LIMIT_MAX || '5'),
    sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '900000'), // 15分钟
    cookieSecure: process.env.COOKIE_SECURE === 'true', // Cookie安全设置
    sameSite: (process.env.COOKIE_SAME_SITE as 'strict' | 'lax' | 'none') ||
              (process.env.NODE_ENV === 'production' ? 'strict' : 'lax') // SameSite策略
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs'
  },

  // 算法执行安全配置
  algorithm: {
    maxExecutionTime: parseInt(process.env.ALGORITHM_MAX_EXECUTION_TIME || '30000'), // 30秒
    maxMemory: parseInt(process.env.ALGORITHM_MAX_MEMORY || '134217728'), // 128MB
    maxCodeSize: parseInt(process.env.ALGORITHM_MAX_CODE_SIZE || '1048576') // 1MB
  },

  // 钉钉授权配置
  dingtalk: {
    clientId: process.env.DINGTALK_CLIENT_ID || '',
    clientSecret: process.env.DINGTALK_CLIENT_SECRET || '',
    host: process.env.DINGTALK_HOST || 'https://oapi.dingtalk.com',
    authToken: process.env.DINGTALK_AUTH_TOKEN || '',
    userInfo: process.env.DINGTALK_USER_INFO || ''
  }
}

// 输出当前配置信息（开发环境）
if (process.env.NODE_ENV === 'development') {
  console.log('配置加载完成:', {
    ...config,
    database: {
      ...config.database,
      // 不在日志中暴露密码
      password: '******'
    }
  })
}

export default config

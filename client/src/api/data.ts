import request from '@/utils/request'
import type { AxiosRequestHeaders } from 'axios'

/**
 * 统一查询钻进数据
 * @param params 查询参数，包含设备ID，可选的文件ID/时间范围/孔号
 */
export const queryDrillingData = (params: {
  deviceId: string;
  fileId?: string;
  startTime?: string;
  endTime?: string;
  holeNo?: string;
  limit?: number;
  offset?: number;
}): Promise<any> => {
  return request({
    url: '/data/query',
    method: 'post',
    data: params,
    headers: {
      'Accept-Encoding': 'gzip, deflate, br',
      'Cache-Control': 'no-cache'
    } as unknown as AxiosRequestHeaders,
    timeout: 300000, // 5分钟超时时间
    maxContentLength: Infinity, // 确保没有响应大小限制
    responseType: 'json'
  })
}

/**
 * 查询原始数据并自动调用指定算法
 * @param params 查询参数，包含设备ID、算法方法名称，可选的文件ID/时间范围/孔号
 */
export const queryDataWithAlgorithms = (params: {
  deviceId: string;
  functions: string; // 算法方法名称，多个用逗号分隔
  fileId?: string;
  startTime?: string;
  endTime?: string;
  holeNo?: string;
  limit?: number;
  offset?: number;
}): Promise<any> => {
  return request({
    url: '/data/query-with-algorithms',
    method: 'post',
    data: params,
    headers: {
      'Accept-Encoding': 'gzip, deflate, br',
      'Cache-Control': 'no-cache'
    } as unknown as AxiosRequestHeaders,
    timeout: 300000, // 5分钟超时时间
    maxContentLength: Infinity, // 确保没有响应大小限制
    responseType: 'json'
  })
}

/**
 * 根据深度范围查询钻进数据
 * @param params 查询参数，包含设备ID、孔号、开始深度、结束深度
 */
export const queryDrillingDataByDepth = (params: {
  deviceId: string;
  holeNo: string;
  startDepth: number;
  endDepth: number;
}): Promise<any> => {
  return request({
    url: '/data/query-by-depth',
    method: 'post',
    data: params,
    headers: {
      'Accept-Encoding': 'gzip, deflate, br',
      'Cache-Control': 'no-cache'
    } as unknown as AxiosRequestHeaders,
    timeout: 300000, // 5分钟超时时间
    maxContentLength: Infinity, // 确保没有响应大小限制
    responseType: 'json'
  })
}

/**
 * 获取设备最新一条数据，用于判断设备在线状态
 * @param deviceId 设备ID
 */
export const getLatestDeviceData = (deviceId: string): Promise<any> => {
  return request({
    url: '/data/latest',
    method: 'get',
    params: { deviceId },
    headers: {} as unknown as AxiosRequestHeaders
  })
}

/**
 * 获取字段映射
 */
export const getFieldMappings = (): Promise<any> => {
  return request({
    url: '/data/field-mappings',
    method: 'get',
    headers: {} as unknown as AxiosRequestHeaders
  })
}

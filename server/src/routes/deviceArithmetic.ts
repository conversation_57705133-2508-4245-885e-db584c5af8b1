import { Router } from 'express'
import {
  getDeviceArithmetic,
  setDeviceArithmetic,
  deleteDeviceArithmetic,
  batchSetDeviceArithmetic,
  getDeviceArithmeticRecords
} from '../controllers/deviceArithmetic'
import { verifyToken } from '../middleware/auth'
import { asyncHandler } from '../utils/asyncHandler'

const router = Router()

// 批量设置设备关联算法
router.post('/batch', asyncHandler(verifyToken), asyncHandler(batchSetDeviceArithmetic))

// 获取设备算法设置历史记录
router.get(
  '/records/:deviceId',
  asyncHandler(verifyToken),
  asyncHandler(getDeviceArithmeticRecords)
)

// 获取设备关联的算法
router.get('/:deviceId', asyncHandler(verifyToken), asyncHandler(getDeviceArithmetic))

// 设置设备关联算法
router.post('/:deviceId', asyncHandler(verifyToken), asyncHandler(setDeviceArithmetic))

// 删除设备关联的算法
router.delete(
  '/:deviceId/:arithmeticType',
  asyncHandler(verifyToken),
  asyncHandler(deleteDeviceArithmetic)
)

export default router

import request from '@/utils/request'
import type { LoginRequest, LoginResponse, LogoutResponse } from '@/types/api'
import type { AxiosRequestHeaders } from 'axios'

/**
 * 用户登录
 * @param data 登录请求参数
 * @returns 登录响应数据
 */
export const login = (data: LoginRequest): Promise<LoginResponse> => {
  return request({
    url: '/users/login',
    method: 'post',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 用户登出
 * @returns 登出响应数据
 */
export const logout = (): Promise<LogoutResponse> => {
  return request({
    url: '/users/logout',
    method: 'post',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 钉钉登录
 * @param code 钉钉授权码
 * @returns 登录响应数据
 */
export const dingTalkLogin = (code: string): Promise<LoginResponse> => {
  return request({
    url: '/users/dingtalk-login',
    method: 'post',
    data: { code },
    headers: {} as AxiosRequestHeaders
  })
}

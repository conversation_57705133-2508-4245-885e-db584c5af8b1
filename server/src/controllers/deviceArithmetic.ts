import { Response, NextFunction, Request } from 'express'
import { Arithmetic, <PERSON><PERSON>, DeviceArithmeticRef, DeviceArithmeticRecord } from '../models'
import { AppError } from '../middleware/error'
import { Op, Transaction } from 'sequelize'
import sequelize from '../config/database'
import { getAlgorithmTypeName, formatDateTime } from '../utils/utils'

/**
 * 设备算法关联API文档
 *
 * 1. GET /api/device-arithmetics/:deviceId
 *    获取设备关联的所有算法(清洗和分析)
 *    响应：
 *    {
 *      "success": true,
 *      "data": [
 *        {
 *          "id": 1,
 *          "deviceId": 123,
 *          "arithmeticId": 456,
 *          "arithmeticType": 0, // 0-清洗算法，1-分析算法
 *          "arithmeticTypeName": "清洗算法",
 *          "arithmeticName": "算法名称",
 *          "createdAt": "2024-06-22 10:00:00",
 *          "modifiedAt": "2024-06-22 11:00:00"
 *        }
 *      ]
 *    }
 *
 * 2. POST /api/device-arithmetics/:deviceId
 *    设置设备关联算法
 *    请求体：
 *    {
 *      "arithmeticId": 456, // 算法ID
 *      "arithmeticType": 0  // 0-清洗算法，1-分析算法
 *    }
 *    响应：
 *    {
 *      "success": true,
 *      "message": "设置设备算法成功"
 *    }
 *
 * 3. DELETE /api/device-arithmetics/:deviceId/:arithmeticType
 *    删除设备关联的指定类型算法
 *    arithmeticType: 0-清洗算法，1-分析算法
 *    响应：
 *    {
 *      "success": true,
 *      "message": "删除设备算法关联成功"
 *    }
 *
 * 4. POST /api/device-arithmetics/batch
 *    批量设置设备算法
 *    请求体：
 *    {
 *      "deviceIds": [1, 2, 3], // 设备ID数组
 *      "arithmeticId": 456,    // 算法ID
 *      "arithmeticType": 0     // 0-清洗算法，1-分析算法
 *    }
 *    响应：
 *    {
 *      "success": true,
 *      "message": "批量设置设备算法成功",
 *      "data": {
 *        "total": 3,          // 总共需处理设备数量
 *        "success": 3,        // 成功处理设备数量
 *        "failed": 0          // 失败处理设备数量
 *      }
 *    }
 *
 * 5. GET /api/device-arithmetics/records/:deviceId
 *    获取设备算法设置历史记录
 *    查询参数：
 *    - startDate: 开始日期 (可选)
 *    - endDate: 结束日期 (可选)
 *    - page: 页码 (可选，默认1)
 *    - pageSize: 每页条数 (可选，默认10)
 *    响应：
 *    {
 *      "success": true,
 *      "data": {
 *        "total": 10,
 *        "list": [
 *          {
 *            "id": 1,
 *            "deviceId": 123,
 *            "arithmeticId": 456,
 *            "arithmeticName": "算法名称",
 *            "arithmeticType": 0, // 通过关联算法表获取
 *            "arithmeticTypeName": "清洗算法",
 *            "createdAt": "2024-06-22 10:00:00"
 *          }
 *        ]
 *      }
 *    }
 */

/**
 * 获取设备关联的算法
 */
export const getDeviceArithmetic = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params

    // 验证设备是否存在
    const device = await Device.findByPk(deviceId)
    if (!device) {
      throw new AppError('设备不存在', 404)
    }

    // 查询设备关联的算法
    const deviceArithmetics = await DeviceArithmeticRef.findAll({
      where: { device_id: deviceId }
    })

    // 如果没有关联算法，返回空数组
    if (deviceArithmetics.length === 0) {
      res.status(200).json({
        success: true,
        data: []
      })
      return
    }

    // 获取所有关联算法的详细信息
    const arithmeticIds = deviceArithmetics.map(item => item.arithmetic_id)
    const arithmetics = await Arithmetic.findAll({
      where: { id: { [Op.in]: arithmeticIds } }
    })

    // 转换为前端需要的格式
    const result = deviceArithmetics
      .map(deviceArithmetic => {
        const arithmetic = arithmetics.find(a => a.id === deviceArithmetic.arithmetic_id)
        if (!arithmetic) return null

        // 使用工具函数获取算法类型名称
        const typeName = getAlgorithmTypeName(arithmetic.type)

        return {
          id: deviceArithmetic.id,
          deviceId: deviceArithmetic.device_id,
          arithmeticId: deviceArithmetic.arithmetic_id,
          arithmeticType: deviceArithmetic.arithmetic_type,
          arithmeticTypeName: typeName,
          arithmeticName: arithmetic.name,
          createdAt: formatDateTime(deviceArithmetic.created_at),
          modifiedAt: deviceArithmetic.modified_at
            ? formatDateTime(deviceArithmetic.modified_at)
            : '未知'
        }
      })
      .filter(Boolean)

    res.status(200).json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('获取设备关联算法失败:', error)
    next(error)
  }
}

/**
 * 设置设备关联算法
 */
export const setDeviceArithmetic = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const transaction: Transaction = await sequelize.transaction()

  try {
    const { deviceId } = req.params
    const { arithmeticId, arithmeticType } = req.body

    if (!arithmeticId || arithmeticType === undefined) {
      throw new AppError('缺少必要参数', 400)
    }

    // 验证设备是否存在
    const device = await Device.findByPk(deviceId)
    if (!device) {
      throw new AppError('设备不存在', 404)
    }

    // 验证算法是否存在
    const arithmetic = await Arithmetic.findByPk(arithmeticId)
    if (!arithmetic) {
      throw new AppError('算法不存在', 404)
    }

    // 验证算法类型是否匹配
    if (arithmetic.type !== arithmeticType) {
      throw new AppError('算法类型不匹配', 400)
    }

    // 如果是分析算法，需要检查product_key是否匹配
    if (arithmeticType === 1 && arithmetic.product_key !== device.product_key) {
      throw new AppError('控件算法与设备类型不匹配', 400)
    }

    // 查找当前设备是否已关联该类型的算法
    const existingRef = await DeviceArithmeticRef.findOne({
      where: {
        device_id: deviceId,
        arithmetic_type: arithmeticType
      },
      transaction
    })

    if (existingRef) {
      // 更新已有关联
      existingRef.arithmetic_id = arithmeticId
      existingRef.modified_at = new Date()
      await existingRef.save({ transaction })
    } else {
      // 创建新关联
      await DeviceArithmeticRef.create(
        {
          device_id: parseInt(deviceId),
          arithmetic_id: arithmeticId,
          arithmetic_type: arithmeticType
        } as any,
        { transaction }
      )
    }

    // 记录设置历史
    await DeviceArithmeticRecord.create(
      {
        device_id: parseInt(deviceId),
        arithmetic_id: arithmeticId
      } as any,
      { transaction }
    )

    await transaction.commit()

    res.status(200).json({
      success: true,
      message: '设置设备算法成功'
    })
  } catch (error) {
    await transaction.rollback()
    console.error('设置设备算法失败:', error)
    next(error)
  }
}

/**
 * 获取算法应用设备数量
 * 为算法列表API扩展功能
 */
export const getArithmeticDeviceCount = async (arithmeticId: number): Promise<number> => {
  try {
    const count = await DeviceArithmeticRef.count({
      where: { arithmetic_id: arithmeticId }
    })
    return count
  } catch (error) {
    console.error('获取算法应用设备数量失败:', error)
    return 0
  }
}

/**
 * 删除设备关联的算法
 */
export const deleteDeviceArithmetic = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId, arithmeticType } = req.params

    // 验证设备是否存在
    const device = await Device.findByPk(deviceId)
    if (!device) {
      throw new AppError('设备不存在', 404)
    }

    // 删除关联
    await DeviceArithmeticRef.destroy({
      where: {
        device_id: deviceId,
        arithmetic_type: arithmeticType
      }
    })

    res.status(200).json({
      success: true,
      message: '删除设备算法关联成功'
    })
  } catch (error) {
    console.error('删除设备算法关联失败:', error)
    next(error)
  }
}

/**
 * 批量设置设备关联算法
 */
export const batchSetDeviceArithmetic = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  const transaction: Transaction = await sequelize.transaction()

  try {
    const { deviceIds, arithmeticId, arithmeticType } = req.body

    if (
      !deviceIds ||
      !Array.isArray(deviceIds) ||
      deviceIds.length === 0 ||
      !arithmeticId ||
      arithmeticType === undefined
    ) {
      throw new AppError('缺少必要参数', 400)
    }

    // 验证算法是否存在
    const arithmetic = await Arithmetic.findByPk(arithmeticId)
    if (!arithmetic) {
      throw new AppError('算法不存在', 404)
    }

    // 获取所有设备
    const devices = await Device.findAll({
      where: { id: { [Op.in]: deviceIds } }
    })

    if (devices.length === 0) {
      throw new AppError('未找到有效设备', 404)
    }

    const results = {
      total: deviceIds.length,
      success: 0,
      failed: 0,
      failedDevices: [] as any[]
    }

    // 逐个处理设备
    for (const device of devices) {
      try {
        // 验证算法类型是否匹配
        if (arithmetic.type !== arithmeticType) {
          throw new Error('算法类型不匹配')
        }

        // 如果是分析算法，需要检查product_key是否匹配
        if (arithmeticType === 1 && arithmetic.product_key !== device.product_key) {
          throw new Error('控件算法与设备类型不匹配')
        }

        // 查找当前设备是否已关联该类型的算法
        const existingRef = await DeviceArithmeticRef.findOne({
          where: {
            device_id: device.id,
            arithmetic_type: arithmeticType
          },
          transaction
        })

        if (existingRef) {
          // 更新已有关联
          existingRef.arithmetic_id = arithmeticId
          existingRef.modified_at = new Date()
          await existingRef.save({ transaction })
        } else {
          // 创建新关联
          await DeviceArithmeticRef.create(
            {
              device_id: device.id,
              arithmetic_id: arithmeticId,
              arithmetic_type: arithmeticType
            } as any,
            { transaction }
          )
        }

        // 记录设置历史
        await DeviceArithmeticRecord.create(
          {
            device_id: device.id,
            arithmetic_id: arithmeticId
          } as any,
          { transaction }
        )

        results.success++
      } catch (error) {
        results.failed++
        results.failedDevices.push({
          deviceId: device.id,
          reason: (error as Error).message
        })
      }
    }

    await transaction.commit()

    res.status(200).json({
      success: true,
      message: '批量设置设备算法成功',
      data: {
        total: results.total,
        success: results.success,
        failed: results.failed,
        failedDevices: results.failedDevices
      }
    })
  } catch (error) {
    await transaction.rollback()
    console.error('批量设置设备算法失败:', error)
    next(error)
  }
}

/**
 * 获取设备算法设置历史记录
 */
export const getDeviceArithmeticRecords = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId } = req.params
    const { startDate, endDate, page = '1', pageSize = '10' } = req.query

    // 验证设备是否存在
    const device = await Device.findByPk(deviceId)
    if (!device) {
      throw new AppError('设备不存在', 404)
    }

    // 构建查询条件
    const where: any = { device_id: deviceId }

    // 日期过滤
    if (startDate && endDate) {
      where.created_at = {
        [Op.between]: [new Date(startDate.toString()), new Date(endDate.toString())]
      }
    } else if (startDate) {
      where.created_at = { [Op.gte]: new Date(startDate.toString()) }
    } else if (endDate) {
      where.created_at = { [Op.lte]: new Date(endDate.toString()) }
    }

    // 分页设置
    const pageNumber = parseInt(page.toString())
    const limit = parseInt(pageSize.toString())
    const offset = (pageNumber - 1) * limit

    // 查询设备算法设置记录
    const { count, rows } = await DeviceArithmeticRecord.findAndCountAll({
      where,
      limit,
      offset,
      order: [['created_at', 'DESC']]
    })

    // 获取所有关联算法的详细信息
    const arithmeticIds = rows.map(item => item.arithmetic_id)
    const arithmetics = await Arithmetic.findAll({
      where: { id: { [Op.in]: arithmeticIds } }
    })

    // 转换为前端需要的格式
    const result = rows
      .map(record => {
        const arithmetic = arithmetics.find(a => a.id === record.arithmetic_id)
        if (!arithmetic) return null

        // 使用工具函数获取算法类型名称
        const typeName = getAlgorithmTypeName(arithmetic.type)

        return {
          id: record.id,
          deviceId: record.device_id,
          arithmeticId: record.arithmetic_id,
          arithmeticName: arithmetic.name,
          arithmeticType: arithmetic.type,
          arithmeticTypeName: typeName,
          createdAt: formatDateTime(record.created_at)
        }
      })
      .filter(Boolean)

    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: result
      }
    })
  } catch (error) {
    console.error('获取设备算法设置历史记录失败:', error)
    next(error)
  }
}

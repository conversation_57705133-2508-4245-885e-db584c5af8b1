import { Router } from 'express'
import {
  getDeviceTemplateConfigs,
  getDeviceEnabledTemplates,
  addDeviceTemplateConfig,
  deleteDeviceTemplateConfig,
  batchConfigDeviceTemplates,
  getTemplateDeviceCount,
  unbindDeviceTemplate
} from '../controllers/deviceTemplateConfig'
import { asyncHandler } from '../utils/asyncHandler'
import { verifyToken } from '../middleware/auth'

const router = Router()

// 获取设备的模板配置列表
router.get('/device/:deviceId', asyncHandler(getDeviceTemplateConfigs))

// 获取设备启用的模板列表
router.get('/device/:deviceId/enabled', asyncHandler(getDeviceEnabledTemplates))

// 获取模板绑定的设备数量
router.get('/template/:templateId/count', asyncHandler(getTemplateDeviceCount))

// 添加设备模板配置
router.post('/device/:deviceId', asyncHandler(verifyToken), asyncHandler(addDeviceTemplateConfig))

// 批量配置设备模板
router.post('/device/:deviceId/batch', asyncHandler(verifyToken), asyncHandler(batchConfigDeviceTemplates))

// 解除设备与模板的绑定
router.delete('/device/:deviceId/template/:templateId', asyncHandler(verifyToken), asyncHandler(unbindDeviceTemplate))

// 删除设备模板配置
router.delete('/:id', asyncHandler(verifyToken), asyncHandler(deleteDeviceTemplateConfig))

export default router 
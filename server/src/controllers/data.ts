import { Response, NextFunction, Request } from 'express'
import { File, Device } from '../models' // 需要导入 File 和 Device 模型
import { AppError } from '../middleware/error'
import { Op } from 'sequelize'
import { convertToCamelCase } from '../utils/utils'
import DeviceArithmeticRef from '../models/deviceArithmeticRef'
import Arithmetic from '../models/arithmetic'
import { AlgorithmExecutor } from '../utils/algorithmExecutor'
import DeviceChartComponentConfig from '../models/deviceChartComponentConfig'
import DeviceTemplateConfig from '../models/deviceTemplateConfig'
import { createDeviceRawDataModel } from '../models/data'
import { logDataProcessing } from '../middleware/logger'

// 算法执行器实例，在整个模块中共享
const algorithmExecutor = new AlgorithmExecutor()

/**
 * 合并统计类数据，处理重复name的问题
 * 用于处理批量处理后产生的重复统计结果
 *
 * 策略：
 * 1. 找到name重复最多的次数
 * 2. 将相同name值累加
 * 3. 将每个name的值除以最大的name重复数量
 */
const mergeStatisticsData = (data: any[]): any[] => {
  if (!Array.isArray(data) || data.length === 0) {
    return data;
  }

  // 使用Map来统计每个name的累加值和出现次数
  const mergedMap = new Map<string, { totalValue: number, count: number, item: any }>();

  data.forEach(item => {
    if (item && typeof item === 'object' && item.name && typeof item.value === 'number') {
      const existingEntry = mergedMap.get(item.name);

      if (existingEntry) {
        // 如果已存在相同name的项，累加value值和计数
        existingEntry.totalValue += item.value;
        existingEntry.count += 1;
      } else {
        // 如果不存在，创建新条目
        mergedMap.set(item.name, {
          totalValue: item.value,
          count: 1,
          item: { ...item } // 保存原始项的其他属性
        });
      }
    }
  });

  // 找到name重复最多的次数
  const maxCount = Math.max(...Array.from(mergedMap.values()).map(entry => entry.count));

  console.log(`最大重复次数: ${maxCount}`);

  // 计算每个name的最终值：累加值除以最大重复次数
  const result = Array.from(mergedMap.entries()).map(([name, entry]) => {
    const finalValue = entry.totalValue / maxCount;
    return {
      ...entry.item,
      name,
      value: Math.round(finalValue * 100) / 100 // 保留两位小数
    };
  });

  // 按value降序排序
  result.sort((a, b) => (b.value || 0) - (a.value || 0));

  console.log(`统计数据合并完成: 原始${data.length}项 -> 合并后${result.length}项`);
  console.log('合并详情:', Array.from(mergedMap.entries()).map(([name, entry]) =>
    `${name}: 出现${entry.count}次，累加值${entry.totalValue.toFixed(2)}，最终值${(entry.totalValue / maxCount).toFixed(2)}%`
  ));

  return result;
};

/**
 * 合并地质分析报告数据，处理重复name的问题
 * 用于处理批量处理后产生的重复地质分析结果
 *
 * 策略：
 * 1. 根据name分组
 * 2. 合并相同name下的description和suggestion
 * 3. 如果内容不同则拼接，如果相同则去重
 */
const mergeGeologicAnalysisData = (data: any[]): any[] => {
  if (!Array.isArray(data) || data.length === 0) {
    return data;
  }

  // 使用Map来合并相同name的数据项
  const mergedMap = new Map<string, { descriptions: Set<string>, suggestions: Set<string>, item: any }>();

  data.forEach(item => {
    if (item && typeof item === 'object' && item.name) {
      const existingEntry = mergedMap.get(item.name);

      if (existingEntry) {
        // 如果已存在相同name的项，合并description和suggestion
        if (item.description && typeof item.description === 'string') {
          existingEntry.descriptions.add(item.description.trim());
        }
        if (item.suggestion && typeof item.suggestion === 'string') {
          existingEntry.suggestions.add(item.suggestion.trim());
        }
      } else {
        // 如果不存在，创建新条目
        const descriptions = new Set<string>();
        const suggestions = new Set<string>();

        if (item.description && typeof item.description === 'string') {
          descriptions.add(item.description.trim());
        }
        if (item.suggestion && typeof item.suggestion === 'string') {
          suggestions.add(item.suggestion.trim());
        }

        mergedMap.set(item.name, {
          descriptions,
          suggestions,
          item: { ...item } // 保存原始项的其他属性
        });
      }
    }
  });

  // 构建最终结果
  const result = Array.from(mergedMap.entries()).map(([name, entry]) => {
    const mergedItem: any = {
      ...entry.item,
      name
    };

    // 合并descriptions（用句号分隔）
    if (entry.descriptions.size > 0) {
      mergedItem.description = Array.from(entry.descriptions).join('。');
    }

    // 合并suggestions（用句号分隔）
    if (entry.suggestions.size > 0) {
      mergedItem.suggestion = Array.from(entry.suggestions).join('。');
    }

    return mergedItem;
  });

  console.log(`地质分析数据合并完成: 原始${data.length}项 -> 合并后${result.length}项`);
  console.log('合并详情:', Array.from(mergedMap.entries()).map(([name, entry]) =>
    `${name}: ${entry.descriptions.size}个描述，${entry.suggestions.size}个建议`
  ));

  return result;
};

/**
 * 使用设备算法处理数据
 * @param deviceId 设备ID
 * @param data 原始数据(已转为驼峰格式)
 */
const processDataWithAlgorithm = async (deviceId: string, data: Record<string, any>[] | Record<string, any>): Promise<Record<string, any>[] | Record<string, any>> => {
  try {
    // 确保data是数组
    let dataArray = Array.isArray(data) ? data : [data];

    // 1. 查询设备关联的清洗算法并执行数据清洗
    const deviceCleaningAlgorithm = await DeviceArithmeticRef.findOne({
      where: {
        device_id: deviceId,
        arithmetic_type: 0 // 0表示清洗算法
      }
    });

    if (deviceCleaningAlgorithm) {
      console.log(`设备 ${deviceId} 关联了清洗算法，开始执行数据清洗`);

      // 获取清洗算法内容
      const cleaningAlgorithm = await Arithmetic.findByPk(deviceCleaningAlgorithm.arithmetic_id);
      if (cleaningAlgorithm && cleaningAlgorithm.content) {
        try {
          // 加载清洗算法
          await algorithmExecutor.loadAlgorithm(cleaningAlgorithm);

          // 获取清洗算法的方法列表
          const cleaningMethods = await algorithmExecutor.getExposedMethods();

          // 执行清洗算法的clean方法
          if (cleaningMethods.includes('clean')) {
            console.log(`执行清洗算法方法: clean`);
            const cleanedData = await algorithmExecutor.executeMethod('clean', dataArray);
            if (cleanedData && Array.isArray(cleanedData)) {
              dataArray = cleanedData;
              logDataProcessing('数据清洗完成', data.length, dataArray.length);
            } else {
              console.warn('清洗算法返回数据格式异常，使用原始数据');
            }
          } else {
            console.warn('清洗算法未提供clean方法');
          }
        } catch (cleaningError) {
          console.error('执行清洗算法失败:', cleaningError);
          // 清洗失败不影响后续流程，继续使用原始数据
        }
      } else {
        console.log(`清洗算法 ${deviceCleaningAlgorithm.arithmetic_id} 不存在或内容为空`);
      }
    } else {
      console.log(`设备 ${deviceId} 没有关联清洗算法，跳过数据清洗步骤`);
    }

    // 2. 查询设备关联的控件算法
    const deviceAlgorithm = await DeviceArithmeticRef.findOne({
      where: {
        device_id: deviceId,
        arithmetic_type: 1 // 1表示控件算法
      }
    });
    // 3. 创建结果对象
    const result: Record<string, any> = {};

    // 如果没有关联控件算法，直接返回清洗后的数据
    if (!deviceAlgorithm) {
      console.log(`设备 ${deviceId} 没有关联控件算法，返回清洗后的数据`);
      result.generateDrillingData = dataArray;
      return result;
    }

    // 4. 获取控件算法内容
    const algorithm = await Arithmetic.findByPk(deviceAlgorithm.arithmetic_id);
    if (!algorithm || !algorithm.content) {
      console.log(`控件算法 ${deviceAlgorithm.arithmetic_id} 不存在或内容为空，返回清洗后的数据`);
      result.generateDrillingData = dataArray;
      return result;
    }

    // 5. 加载控件算法
    await algorithmExecutor.loadAlgorithm(algorithm);

    // 6. 获取控件算法的所有方法
    const methods = await algorithmExecutor.getExposedMethods();

    // 7. 精度处理方法
    const progressMethod = 'dataPrecisionProcessing'
    // 查询控件算法，是否有精度处理算法方法
    if (methods.includes(progressMethod)) {
      const progressData = await algorithmExecutor.executeMethod(progressMethod, dataArray);
      dataArray = progressData;
    }

    // 8. 获取设备已启用的图表组件的方法名称（包括直接配置的组件和模板中的组件）
    let requiredMethods: string[] = [];

    try {
      // 7.1 获取设备直接配置的图表组件
      const directChartsResponse = await DeviceChartComponentConfig.getEnabledChartsByDeviceId(parseInt(deviceId));
      let directChartMethods: string[] = [];

      if (directChartsResponse.success && directChartsResponse.data && directChartsResponse.data.list) {
        directChartMethods = directChartsResponse.data.list
          .filter(chart => chart.functionNames && chart.functionNames.length > 0) // 只保留有functionNames的图表
          .flatMap(chart => chart.functionNames); // 展开所有函数名
      }

      // 7.2 获取设备绑定模板中的图表组件
      const templateChartsResponse = await DeviceTemplateConfig.getEnabledTemplatesByDeviceId(parseInt(deviceId));
      let templateChartMethods: string[] = [];

      if (templateChartsResponse.success && templateChartsResponse.data && templateChartsResponse.data.list) {
        const enabledTemplates = templateChartsResponse.data.list;

        // 遍历每个启用的模板，解析其配置中的组件
        for (const templateData of enabledTemplates) {
          // templateData.configObj 包含了解析后的模板配置
          if (templateData.configObj && templateData.configObj.components) {
            try {
              const components = templateData.configObj.components || [];
              // 从模板组件中提取方法名
              const templateMethods: string[] = [];

              for (const comp of components) {
                if (comp.config) {
                  try {
                    // 解析组件的config字段（JSON字符串）
                    const compConfig = JSON.parse(comp.config);
                    if (compConfig.function) {
                      // 处理逗号分隔的多个函数名
                      const functionStr = compConfig.function;
                      const functionNames = functionStr.split(',').map((fn: string) => fn.trim()).filter((fn: string) => fn.length > 0);
                      templateMethods.push(...functionNames);
                    }
                  } catch (configParseError) {
                    console.error(`解析组件 ${comp.id} 的config失败:`, configParseError);
                  }
                }
              }

              templateChartMethods.push(...templateMethods);
            } catch (parseError) {
              console.error(`解析模板 ${templateData.id} 配置失败:`, parseError);
            }
          }
        }
      }
      console.log(`设备 ${deviceId} 直接配置的图表组件方法:`, directChartMethods);
      console.log(`设备 ${deviceId} 模板中的图表组件方法:`, templateChartMethods);

      // 8.3 合并并去重：取直接配置和模板配置的并集
      const allMethods = [...directChartMethods, ...templateChartMethods];
      requiredMethods = [...new Set(allMethods)]; // 使用Set去重
    } catch (error) {
      console.error('获取设备已启用图表组件失败:', error);
    }

    // 如果没有获取到方法或发生错误，使用默认方法列表
    if (requiredMethods.length === 0) {
      console.log('未找到已启用图表的方法，使用默认方法列表');
      requiredMethods = [
        'generateRockyNaturePieChartData',
        'generatePerimeterRockStatisticsBarChartData',
        'generateGeologicAnalysisReport',
        'generateStrataDistributionLineChartData',
        'generateConditionMatrixChartData',
        'generateDrillingData',
        'generateDrillingCurveFilteringData',
        'generateDrillingRecordData',
        'generateDrillingDepthTimeSeriesData'
      ];
    }

    // 记录日志
    console.log(`使用控件算法 ${algorithm.id} 处理清洗后的数据，数据量: ${dataArray.length}`);

    // 9. 逐个执行方法调用，避免一个方法失败影响其他方法
    for (const methodName of requiredMethods) {
      if (methods.includes(methodName)) {
        try {
          console.log(`执行算法方法: ${methodName}`);
          const methodResult = await algorithmExecutor.executeMethod(methodName, dataArray);
          // 如果是钻进数据，需要将数据通过采集时间进行倒叙排序，也就是数组倒叙
          if (methodName === 'generateDrillingData') {
            methodResult.reverse();
          }
          // 如果是岩石性质饼图 || 围岩统计柱状图，需要合并重复的name数据
          if (methodName === 'generateRockyNaturePieChartData' || methodName === 'generatePerimeterRockStatisticsBarChartData') {
            result[methodName] = mergeStatisticsData(methodResult);
          } else if (methodName === 'generateGeologicAnalysisReport') {
            // 如果是地质分析报告，需要合并重复name的描述和建议
            result[methodName] = mergeGeologicAnalysisData(methodResult);
          } else {
            result[methodName] = methodResult;
          }
        } catch (methodError) {
          console.error(`执行算法方法 ${methodName} 失败:`, methodError);
          result[methodName] = null;
        }
      } else {
        console.log(`算法未提供方法: ${methodName}`);
        result[methodName] = null;
      }
    }
    
    // 10. 返回所有结果
    return result;
  } catch (error) {
    console.error('使用算法处理数据失败:', error);
    // 失败也返回原始数据，不影响查询结果
    return data;
  }
};

/**
 * 统一数据查询接口
 */
export const queryData = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId, fileId, startTime, endTime, holeNo, limit = 1000, offset = 0 } = req.body;
    const startQueryTime = Date.now();
    
    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400);
    }

    // 1. 获取设备序列号
    const device = await Device.findByPk(deviceId);
    if (!device || !device.device_sn) {
      throw new AppError('设备不存在或缺少序列号', 404);
    }
    const device_sn = device.device_sn;

    // 2. 构建原始数据表名（正确处理连字符转换为下划线）
    const tableName = `dh_original_data_${device_sn.replace(/-/g, '_').toLowerCase()}`;
    console.log(`查询设备 ${deviceId}(${device_sn}) 的数据表: ${tableName}`);

    let finalStartTime = startTime;
    let finalEndTime = endTime;
    
    // 3. 如果提供了文件ID，则通过文件ID获取时间范围(如果未直接提供)
    if (fileId) {
      try {
        // 查询文件信息，获取数据的时间范围
        const fileInfo = await File.findByPk(fileId);
        if (!fileInfo) {
          throw new AppError('文件不存在', 404);
        }
        
        console.log(`文件 ${fileId} 的时间范围: ${fileInfo.first_data_time} 至 ${fileInfo.last_data_time}`);
        
        // 从文件记录中获取时间范围
        if (!finalStartTime && fileInfo.first_data_time) finalStartTime = fileInfo.first_data_time;
        if (!finalEndTime && fileInfo.last_data_time) finalEndTime = fileInfo.last_data_time;
      } catch (error) {
        console.error('获取文件时间范围失败:', error);
        if (error instanceof AppError) {
          throw error; // 重新抛出应用程序特定错误
        } else {
          throw new AppError('获取文件时间范围失败', 500);
        }
      }
    }

    // 4. 构建查询条件
    const whereConditions: any = {};
    
    // 添加时间范围条件(如果有)
    if (finalStartTime && finalEndTime) {
      whereConditions.collection_at = {
        [Op.between]: [finalStartTime, finalEndTime]
      };
    } else if (finalStartTime) {
      whereConditions.collection_at = {
        [Op.gte]: finalStartTime
      };
    } else if (finalEndTime) {
      whereConditions.collection_at = {
        [Op.lte]: finalEndTime
      };
    }
    
    // 添加孔号条件(如果有)
    if (holeNo) {
      whereConditions.hl_num = holeNo;
    }
    
    // 确保至少有一个查询条件
    if (Object.keys(whereConditions).length === 0) {
      throw new AppError('至少需要提供时间范围或孔号作为查询条件', 400);
    }
    
    // 5. 使用动态模型查询数据
    const sqlStartTime = Date.now();
    console.log('执行设备数据查询:', whereConditions);

    try {
      // 创建设备原始数据模型
      const DeviceRawDataModel = createDeviceRawDataModel(device_sn);
      
      // 使用模型查询数据
      const originalData = await DeviceRawDataModel.findAll({
        where: whereConditions,
        order: [['collection_at', 'ASC']],
        limit,
        offset
      });      
      
      const sqlEndTime = Date.now();
      console.log(`SQL查询执行耗时: ${sqlEndTime - sqlStartTime}ms, 查询结果: ${originalData.length}条记录`);
      
      // 如果没有数据返回空结果
      if (!originalData || originalData.length === 0) {
        console.log(`设备 ${deviceId} 没有查询到符合条件的数据`);
        
        // 获取设备已启用的图表组件
        let emptyResult: Record<string, any> = {
          generateDrillingData: [] // 确保基本的钻进数据字段总是存在
        };
        
        try {
          // 获取启用的图表组件方法列表
          const chartsResponse = await DeviceChartComponentConfig.getEnabledChartsByDeviceId(parseInt(deviceId));
          
          if (chartsResponse.success && chartsResponse.data && chartsResponse.data.list) {
            // 基于启用的图表组件创建空结果
            chartsResponse.data.list.forEach(chart => {
              if (chart.functionNames && chart.functionNames.length > 0) {
                // 为每个函数名创建空数据结构
                chart.functionNames.forEach(functionName => {
                  // 为不同类型的图表返回合适的空数据结构
                  if (functionName === 'generateStrataDistributionLineChartData') {
                    emptyResult[functionName] = { data: [] };
                  } else {
                    emptyResult[functionName] = [];
                  }
                });
              }
            });
          }
        } catch (error) {
          console.error('获取已启用图表组件失败:', error);
          // 使用默认的空结果结构
          emptyResult = {
            generateDrillingData: [],
            generateRockyNaturePieChartData: [],
            generatePerimeterRockStatisticsBarChartData: [],
            generateGeologicAnalysisReport: [],
            generateStrataDistributionLineChartData: { data: [] },
            generateConditionMatrixChartData: []
          };
        }
        
        res.json({
          success: true,
          message: '没有查询到符合条件的数据',
          data: emptyResult
        });
        return;
      }
      
      // 7. 数据格式转换与处理
      const formatStartTime = Date.now();
      
      // 将Sequelize模型实例转换为普通对象
      const plainData = originalData.map(item => item.get({ plain: true }));
      
      // 使用导入的convertToCamelCase函数
      const camelCaseData = convertToCamelCase(plainData);
      const formatEndTime = Date.now();
      
      console.log(`数据格式转换耗时: ${formatEndTime - formatStartTime}ms`);
      
      // 8. 使用设备算法处理数据
      const processStartTime = Date.now();
      const processedData = await processDataWithAlgorithm(deviceId, camelCaseData);
      const processEndTime = Date.now();
      
      console.log(`数据处理耗时: ${processEndTime - processStartTime}ms`);
      
      // 9. 返回结果
      const totalTime = Date.now() - startQueryTime;
      console.log(`总查询耗时: ${totalTime}ms`);
      
      res.json({
        success: true,
        message: '查询成功',
        data: processedData
      });
    } catch (error: any) {
      console.error('查询数据失败:', error);
      if (error.name === 'SequelizeDatabaseError') {
        throw new AppError(`查询数据失败，请检查数据表 ${tableName} 是否存在`, 500);
      } else {
        throw error;
      }
    }
  } catch (error) {
    next(error);
  }
};

/**
 * 获取字段名称映射表
 * 提供给前端使用，用于显示表格列标题
 */
export const getFieldMappings = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // 定义字段映射
    const fieldMappings = [
      {"id": "主键ID，自增"},
      {"collectionAt": "采集时间，唯一值"},
      {"deviceSn": "设备序列号"},
      {"heart": "心跳号"},
      {"mode": "钻机工作模式"},
      {"strkPct": "钻机行程百分比"},
      {"18b03": "预留字段：18B03"},
      {"wrmCd": "告警码"},
      {"dpth": "钻孔深度(CM)"},
      {"rtnTq": "旋转扭矩"},
      {"frcstKn": "推进力"},
      {"wtrPrsH": "高压水压力"},
      {"rtnSpd": "旋转速度"},
      {"advncSpd": "钻进速度"},
      {"hydrPrs": "液压压力"},
      {"wtrPrsL": "低压水压力"},
      {"hghWrk": "工作时长-高位"},
      {"lwWrk": "工作时长-低位"},
      {"rtnPrs": "旋转压力"},
      {"frcstPrs": "推进压力"},
      {"clctType": "采集类型"},
      {"clctSts": "采集状态"},
      {"tunnelDpth": "隧道深度(标段号)"},
      {"hlNum": "孔号"},
      {"hlAng": "孔倾角"},
      {"rc0": "RC0"},
      {"rc1": "RC1"},
      {"rc2": "RC2"},
      {"rc3": "RC3"},
      {"rc4": "RC4"},
      {"rc5": "RC5"},
      {"rc6": "RC6"},
      {"led_08": "LED_08"},
      {"ledAf": "LED_AF"},
      {"tunnelName": "隧道名称(项目名称)"},
      {"diameter": "钻头直径，默认90"}
    ];

    res.status(200).json({
      success: true,
      data: fieldMappings
    });
  } catch (error) {
    console.error('获取字段映射失败:', error);
    next(error);
  }
};

/**
 * 获取设备最新一条数据，用于判断设备在线状态
 */
export const getLatestDeviceData = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const deviceId = req.query.deviceId as string;
    
    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400);
    }

    // 获取设备序列号
    const device = await Device.findByPk(deviceId);
    if (!device || !device.device_sn) {
      throw new AppError('设备不存在或缺少序列号', 404);
    }
    const device_sn = device.device_sn;

    // 构建原始数据表名
    const tableName = `dh_original_data_${device_sn.replace(/-/g, '_').toLowerCase()}`;
    console.log(`查询设备 ${deviceId}(${device_sn}) 的最新数据`);

    // 查询最新的一条数据
    try {
      // 创建设备原始数据模型
      const DeviceRawDataModel = createDeviceRawDataModel(device_sn);
      
      // 查询最新一条数据
      const latestData = await DeviceRawDataModel.findOne({
        order: [['collection_at', 'DESC']]
      });
      
      // 如果没有数据
      if (!latestData) {
        res.json({
          success: true,
          message: '没有查询到数据',
          data: null
        });
        return;
      }
      
      // 转换为纯 JavaScript 对象
      const plainData = latestData.get({ plain: true });
      
      // 数据格式转换
      const camelCaseData = convertToCamelCase(plainData);
      
      res.json({
        success: true,
        message: '查询成功',
        data: camelCaseData
      });
    } catch (error: any) {
      console.error('查询最新数据失败:', error);
      if (error.name === 'SequelizeDatabaseError') {
        throw new AppError(`查询数据失败，请检查数据表 ${tableName} 是否存在`, 500);
      } else {
        throw error;
      }
    }
  } catch (error) {
    next(error);
  }
}

/**
 * 查询原始数据并自动调用指定算法
 * 基于 queryData 接口，增加 functions 参数用于指定需要调用的算法方法
 */
export const queryDataWithAlgorithms = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId, fileId, startTime, endTime, holeNo, limit = 1000, offset = 0, functions } = req.body;
    const startQueryTime = Date.now();

    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400);
    }

    if (!functions || typeof functions !== 'string') {
      throw new AppError('functions参数不能为空，且必须为字符串', 400);
    }

    // 解析算法方法名称列表
    const algorithmMethods = functions.split(',').map((method: string) => method.trim()).filter((method: string) => method.length > 0);

    if (algorithmMethods.length === 0) {
      throw new AppError('至少需要指定一个算法方法', 400);
    }

    console.log(`查询数据并调用算法方法: ${algorithmMethods.join(', ')}`);

    // 1. 获取设备序列号
    const device = await Device.findByPk(deviceId);
    if (!device || !device.device_sn) {
      throw new AppError('设备不存在或缺少序列号', 404);
    }
    const device_sn = device.device_sn;

    // 2. 构建原始数据表名
    const tableName = `dh_original_data_${device_sn.replace(/-/g, '_').toLowerCase()}`;
    console.log(`查询设备 ${deviceId}(${device_sn}) 的数据表: ${tableName}`);

    // 3. 处理时间参数
    let finalStartTime: Date | undefined;
    let finalEndTime: Date | undefined;

    if (fileId) {
      // 如果提供了文件ID，从文件记录中获取时间范围
      const file = await File.findByPk(fileId);
      if (!file) {
        throw new AppError('文件不存在', 404);
      }
      finalStartTime = file.first_data_time;
      finalEndTime = file.last_data_time;
      console.log(`使用文件时间范围: ${finalStartTime} - ${finalEndTime}`);
    } else {
      // 使用传入的时间参数
      if (startTime) finalStartTime = new Date(startTime);
      if (endTime) finalEndTime = new Date(endTime);
      console.log(`使用传入时间范围: ${finalStartTime} - ${finalEndTime}`);
    }

    // 4. 构建查询条件
    const whereConditions: any = {};

    // 添加时间范围条件(如果有)
    if (finalStartTime && finalEndTime) {
      whereConditions.collection_at = {
        [Op.between]: [finalStartTime, finalEndTime]
      };
    } else if (finalStartTime) {
      whereConditions.collection_at = {
        [Op.gte]: finalStartTime
      };
    } else if (finalEndTime) {
      whereConditions.collection_at = {
        [Op.lte]: finalEndTime
      };
    }

    // 添加孔号条件(如果有)
    if (holeNo) {
      whereConditions.hl_num = holeNo;
    }

    // 确保至少有一个查询条件
    if (Object.keys(whereConditions).length === 0) {
      throw new AppError('至少需要提供时间范围或孔号作为查询条件', 400);
    }

    // 5. 使用动态模型查询数据
    const sqlStartTime = Date.now();
    console.log('执行设备数据查询:', whereConditions);

    try {
      // 创建设备原始数据模型
      const DeviceRawDataModel = createDeviceRawDataModel(device_sn);

      // 使用模型查询数据
      const originalData = await DeviceRawDataModel.findAll({
        where: whereConditions,
        order: [['collection_at', 'ASC']],
        limit,
        offset
      });

      const sqlEndTime = Date.now();
      console.log(`SQL查询执行耗时: ${sqlEndTime - sqlStartTime}ms, 查询结果: ${originalData.length}条记录`);

      // 如果没有数据返回空结果
      if (!originalData || originalData.length === 0) {
        console.log(`设备 ${deviceId} 没有查询到符合条件的数据`);
        res.json({
          success: true,
          message: '没有查询到符合条件的数据',
          data: {
            originalData: [],
            algorithmResults: {}
          }
        });
        return;
      }

      // 6. 数据格式转换
      const formatStartTime = Date.now();

      // 将Sequelize模型实例转换为普通对象
      const plainData = originalData.map(item => item.get({ plain: true }));

      // 使用导入的convertToCamelCase函数
      const camelCaseData = convertToCamelCase(plainData);
      const formatEndTime = Date.now();

      console.log(`数据格式转换耗时: ${formatEndTime - formatStartTime}ms`);

      // 7. 执行指定的算法方法
      const algorithmStartTime = Date.now();
      const algorithmResults: Record<string, any> = {};

      // 查询设备关联的分析算法
      const deviceAlgorithm = await DeviceArithmeticRef.findOne({
        where: {
          device_id: deviceId,
          arithmetic_type: 1 // 1表示分析算法
        }
      });

      if (!deviceAlgorithm) {
        throw new AppError('设备未关联分析算法', 400);
      }

      // 获取算法内容
      const algorithm = await Arithmetic.findByPk(deviceAlgorithm.arithmetic_id);
      if (!algorithm || !algorithm.content) {
        throw new AppError('算法不存在或内容为空', 400);
      }

      // 加载算法
      await algorithmExecutor.loadAlgorithm(algorithm);

      // 获取算法的所有可用方法
      const availableMethods = await algorithmExecutor.getExposedMethods();

      // 验证请求的方法是否存在
      const invalidMethods = algorithmMethods.filter(method => !availableMethods.includes(method));
      if (invalidMethods.length > 0) {
        throw new AppError(`算法未提供以下方法: ${invalidMethods.join(', ')}`, 400);
      }

      // 7.1 如果设备绑定了清洗算法，则先执行清洗算法
      const processStartTime = Date.now();
      let processedData = camelCaseData;

      const deviceCleaningAlgorithm = await DeviceArithmeticRef.findOne({
        where: {
          device_id: deviceId,
          arithmetic_type: 0 // 0表示清洗算法
        }
      });

      if (deviceCleaningAlgorithm) {
        console.log(`设备 ${deviceId} 关联了清洗算法，开始执行数据清洗`);

        // 获取清洗算法内容
        const cleaningAlgorithm = await Arithmetic.findByPk(deviceCleaningAlgorithm.arithmetic_id);
        if (cleaningAlgorithm && cleaningAlgorithm.content) {
          try {
            // 加载清洗算法
            await algorithmExecutor.loadAlgorithm(cleaningAlgorithm);

            // 获取清洗算法的方法列表
            const cleaningMethods = await algorithmExecutor.getExposedMethods();

            // 执行清洗算法的clean方法
            if (cleaningMethods.includes('clean')) {
              console.log(`执行清洗算法方法: clean`);
              const cleanedData = await algorithmExecutor.executeMethod('clean', processedData);
              if (cleanedData && Array.isArray(cleanedData)) {
                processedData = cleanedData;
                logDataProcessing('数据清洗完成', camelCaseData.length, processedData.length);
              } else {
                console.warn('清洗算法返回数据格式异常，使用原始数据');
              }
            } else {
              console.warn('清洗算法未提供clean方法');
            }
          } catch (cleaningError) {
            console.error('执行清洗算法失败:', cleaningError);
            // 清洗失败不影响后续流程，继续使用原始数据
          }
        } else {
          console.log(`清洗算法 ${deviceCleaningAlgorithm.arithmetic_id} 不存在或内容为空`);
        }
      } else {
        console.log(`设备 ${deviceId} 没有关联清洗算法，跳过数据清洗步骤`);
      }

      // 7.2 如果设备绑定了控件算法，则执行精度处理算法
      if (deviceAlgorithm) {
        console.log(`设备 ${deviceId} 关联了控件算法，开始执行精度处理`);

        // 重新加载控件算法（确保算法已加载）
        await algorithmExecutor.loadAlgorithm(algorithm);

        // 获取控件算法的方法列表
        const controlMethods = await algorithmExecutor.getExposedMethods();

        // 执行精度处理方法
        const precisionMethod = 'dataPrecisionProcessing';
        if (controlMethods.includes(precisionMethod)) {
          console.log(`执行精度处理方法: ${precisionMethod}`);
          try {
            const precisionData = await algorithmExecutor.executeMethod(precisionMethod, processedData);
            if (precisionData && Array.isArray(precisionData)) {
              processedData = precisionData;
              console.log('数据精度处理完成');
            } else {
              console.warn('精度处理算法返回数据格式异常，使用清洗后的数据');
            }
          } catch (precisionError) {
            console.error('执行精度处理失败:', precisionError);
            // 精度处理失败不影响后续流程，继续使用清洗后的数据
          }
        } else {
          console.warn('控件算法未提供dataPrecisionProcessing方法');
        }
      } else {
        console.log(`设备 ${deviceId} 没有关联控件算法，跳过精度处理步骤`);
      }

      const processEndTime = Date.now();
      console.log(`数据清洗和精度处理耗时: ${processEndTime - processStartTime}ms`);

      // 8. 检查是否包含滤波算法，如果包含则先执行滤波
      let currentProcessedData = processedData; // 当前处理的数据
      const filteringMethodName = 'generateDrillingCurveFilteringData';

      if (algorithmMethods.includes(filteringMethodName)) {
        try {
          console.log(`检测到滤波算法，先执行滤波处理: ${filteringMethodName}`);
          const filteringResult = await algorithmExecutor.executeMethod(filteringMethodName, processedData);

          if (Array.isArray(filteringResult) && filteringResult.length > 0) {
            currentProcessedData = filteringResult; // 更新当前处理的数据为滤波后的数据
            console.log(`滤波算法执行成功，滤波后数据量: ${filteringResult.length}`);
          } else {
            console.warn('滤波算法返回空数据或格式异常，继续使用原始处理数据');
          }
        } catch (filteringError) {
          console.error(`执行滤波算法失败:`, filteringError);
        }

        // 从算法列表中移除滤波算法，避免重复执行
        const remainingMethods = algorithmMethods.filter(method => method !== filteringMethodName);
        algorithmMethods.length = 0; // 清空原数组
        algorithmMethods.push(...remainingMethods); // 添加剩余的算法
      }

      // 9. 依次执行剩余的算法方法（使用滤波后的数据或原始处理数据）
      for (const methodName of algorithmMethods) {
        try {
          console.log(`执行算法方法: ${methodName}`);
          const methodResult = await algorithmExecutor.executeMethod(methodName, currentProcessedData);

          // 特殊处理某些方法的返回结果
          if (methodName === 'generateDrillingData') {
            // 钻进数据需要按采集时间倒序排列
            algorithmResults[methodName] = Array.isArray(methodResult) ? methodResult.reverse() : methodResult;
          } else if (methodName === 'generateRockyNaturePieChartData' || methodName === 'generatePerimeterRockStatisticsBarChartData') {
            // 统计数据需要合并重复的name数据
            algorithmResults[methodName] = mergeStatisticsData(methodResult);
          } else if (methodName === 'generateGeologicAnalysisReport') {
            // 地质分析报告需要合并重复name的描述和建议
            algorithmResults[methodName] = mergeGeologicAnalysisData(methodResult);
          } else {
            algorithmResults[methodName] = methodResult;
          }

          console.log(`算法方法 ${methodName} 执行成功`);
        } catch (methodError) {
          console.error(`执行算法方法 ${methodName} 失败:`, methodError);
          algorithmResults[methodName] = {
            error: `执行失败: ${methodError instanceof Error ? methodError.message : String(methodError)}`
          };
        }
      }

      const algorithmEndTime = Date.now();
      console.log(`算法执行耗时: ${algorithmEndTime - algorithmStartTime}ms`);

      // 10. 返回结果
      const totalTime = Date.now() - startQueryTime;
      console.log(`总查询耗时: ${totalTime}ms`);

      res.json({
        success: true,
        message: '查询并执行算法成功',
        data: algorithmResults
      });
    } catch (error: any) {
      console.error('查询数据失败:', error);
      if (error.name === 'SequelizeDatabaseError') {
        throw new AppError(`查询数据失败，请检查数据表 ${tableName} 是否存在`, 500);
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('查询数据并执行算法失败:', error);
    next(error);
  }
};

/**
 * 根据深度范围查询数据 - 新接口
 * 通过设备ID、孔号、开始深度和结束深度查询原始数据
 */
export const queryDataByDepth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { deviceId, holeNo, startDepth, endDepth } = req.body;
    const startQueryTime = Date.now();
    
    // 验证参数
    if (!deviceId) {
      throw new AppError('设备ID不能为空', 400);
    }
    
    if (!holeNo) {
      throw new AppError('孔号不能为空', 400);
    }
    
    if (startDepth === undefined || endDepth === undefined) {
      throw new AppError('开始深度和结束深度不能为空', 400);
    }
    
    if (parseFloat(startDepth) >= parseFloat(endDepth)) {
      throw new AppError('结束深度必须大于开始深度', 400);
    }

    // 1. 获取设备序列号
    const device = await Device.findByPk(deviceId);
    if (!device || !device.device_sn) {
      throw new AppError('设备不存在或缺少序列号', 404);
    }
    const device_sn = device.device_sn;

    // 2. 构建原始数据表名（正确处理连字符转换为下划线）
    const tableName = `dh_original_data_${device_sn.replace(/-/g, '_').toLowerCase()}`;
    console.log(`根据深度查询设备 ${deviceId}(${device_sn}) 的数据, 孔号: ${holeNo}, 深度范围: ${startDepth}-${endDepth}`);

    // 3. 构建查询条件
    const whereConditions: any = {
      hl_num: holeNo,
      dpth: {
        [Op.between]: [parseFloat(startDepth), parseFloat(endDepth)]
      }
    };
    
    // 4. 使用模型进行查询
    const sqlStartTime = Date.now();
    console.log('执行按深度查询:', whereConditions);

    try {
      // 创建设备原始数据模型
      const DeviceRawDataModel = createDeviceRawDataModel(device_sn);
      
      // 原始查询所有匹配深度范围的数据
      const rawResults = await DeviceRawDataModel.findAll({
        where: whereConditions,
        order: [['dpth', 'ASC']]
      });
      
      // 去除重复深度，只保留每个深度的第一条记录
      const depthMap = new Map();
      const originalData = [];
      
      for (const record of rawResults) {
        const plainRecord = record.get({ plain: true });
        const depth = plainRecord.dpth;
        
        if (!depthMap.has(depth)) {
          depthMap.set(depth, true);
          originalData.push(plainRecord);
        }
        
        // 最多返回1000条记录
        if (originalData.length >= 1000) break;
      }
      
      const sqlEndTime = Date.now();
      console.log(`SQL查询执行耗时: ${sqlEndTime - sqlStartTime}ms, 查询结果: ${originalData.length}条记录`);
      
      // 如果没有数据返回空结果
      if (originalData.length === 0) {
        console.log(`设备 ${deviceId} 没有查询到符合深度条件的数据`);
        res.json({
          success: true,
          message: '没有查询到符合深度条件的数据',
          data: []
        });
        return;
      }
      
      // 6. 数据格式转换与处理
      const formatStartTime = Date.now();
      
      // 使用导入的convertToCamelCase函数
      const camelCaseData = convertToCamelCase(originalData);
      const formatEndTime = Date.now();
      
      console.log(`数据格式转换耗时: ${formatEndTime - formatStartTime}ms`);
      
      // 7. 返回结果
      const totalTime = Date.now() - startQueryTime;
      console.log(`总查询耗时: ${totalTime}ms`);
      
      res.json({
        success: true,
        message: '查询成功',
        data: camelCaseData
      });
    } catch (error: any) {
      console.error('查询数据失败:', error);
      if (error.name === 'SequelizeDatabaseError') {
        throw new AppError(`查询数据失败，请检查数据表 ${tableName} 是否存在`, 500);
      } else {
        throw error;
      }
    }
  } catch (error) {
    next(error);
  }
};



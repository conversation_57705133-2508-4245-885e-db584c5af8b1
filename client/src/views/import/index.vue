<template>
  <div class="import-container">
    <!-- 搜索和列表整合在一个卡片中 -->
    <el-card class="import-list-card">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="24">
          <el-col :span="4">
            <el-input
              v-model="searchForm.fileName"
              placeholder="请输入文件名称"
              clearable
              style="width: 100%"
            />
          </el-col>
          <el-col :span="8">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-col>
          <el-col
            :span="4"
            class="filter-buttons"
          >
            <el-button
              type="primary"
              @click="debouncedHandleSearch"
            >
              搜索
            </el-button>
            <el-button @click="resetForm">
              清空
            </el-button>
          </el-col>
          <el-col
            :span="8"
            style="text-align: right"
          >
            <el-button
              type="primary"
              @click="dialogVisible = true"
            >
              生产导入
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 导入列表 -->
      <el-table
        v-loading="loading"
        :data="importList"
        style="width: 100%"
        border
      >
        <el-table-column
          prop="fileName"
          label="导入文件名称"
          align="center"
        />
        <el-table-column
          prop="importRows"
          label="导入设备数"
          align="center"
        />
        <el-table-column
          prop="createdAt"
          label="导入时间"
          align="center"
        />
        <el-table-column
          label="操作"
          width="150"
          align="center"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="viewImportDetail(scope.row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页部分 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="currentPage"
          :page-size="pageSize"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 导入文件对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="生产导入"
      width="500px"
      class="import-dialog"
    >
      <el-form
        :model="importForm"
        label-width="0"
      >
        <el-form-item>
          <div class="upload-container">
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              drag
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              accept=".csv,text/csv"
              multiple
              :limit="10"
            >
              <el-icon class="el-icon--upload">
                <upload-filled />
              </el-icon>
              <div class="el-upload__text">
                拖拽文件到此处或 <em>点击上传</em>
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  仅支持 .csv 格式文件，最多可选择10个文件
                  <el-button
                    type="primary"
                    link
                    @click="downloadTemplate"
                  >
                    下载模板
                  </el-button>
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button
            type="primary"
            :loading="submitting"
            @click="submitImport"
          >确认导入</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { getImportList, createImport } from '@/api'
import { debouncedApiCall } from '@/utils/debounce'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  fileName: '',
  dateRange: []
})

// 导入表单
const importForm = reactive({
  fileItems: [] // 存储文件项，每项包含file、content、fileName、rows等信息
})

// 加载状态
const loading = ref(false)
const submitting = ref(false)

// 导入列表数据
const importList = ref([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 对话框控制
const dialogVisible = ref(false)
// 上传组件引用
const uploadRef = ref(null)

// 监听对话框关闭事件
watch(dialogVisible, newVal => {
  if (!newVal) {
    // 对话框关闭时重置表单和清空已上传文件
    resetImportForm()
  }
})

// 重置导入表单
const resetImportForm = () => {
  // 重置表单数据
  importForm.fileItems = []

  // 清空上传的文件列表
  if (uploadRef.value) {
    // 使用uploadRef的clearFiles方法清空文件列表
    uploadRef.value.clearFiles()

    // 确保uploadFiles数组也被清空
    if (uploadRef.value.uploadFiles) {
      uploadRef.value.uploadFiles.length = 0
    }
  }
}

// 获取导入列表数据
const fetchImportList = async () => {
  loading.value = true
  try {
    const params = {
      file_name: searchForm.fileName,
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 添加时间段参数
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      Object.assign(params, {
        start_date: searchForm.dateRange[0],
        end_date: searchForm.dateRange[1]
      })
    }

    const response = await getImportList(params)

    if (response.success) {
      importList.value = response.data.list
      total.value = response.data.total
    } else {
      ElMessage.error('获取导入列表失败')
    }
  } catch (error) {
    console.error('获取导入列表出错:', error)
    ElMessage.error('获取导入列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchImportList()
}

// 使用防抖的搜索函数
const debouncedHandleSearch = debouncedApiCall(() => {
  currentPage.value = 1
  fetchImportList()
})

// 重置表单
const resetForm = () => {
  searchForm.fileName = ''
  searchForm.dateRange = []
  handleSearch()
}

// 文件变更处理
const handleFileChange = (file, _fileList) => {
  // 对于新添加的文件进行处理
  if (file.status === 'ready') {
    const fileName = file.name || ''
    const fileExtension = fileName.slice(fileName.lastIndexOf('.')).toLowerCase()

    // 检查文件类型
    if (fileExtension !== '.csv') {
      ElMessage.warning(`文件 ${fileName} 不是.csv格式，已过滤`)
      // 从上传列表中移除非CSV文件
      if (uploadRef.value) {
        uploadRef.value.handleRemove(file)
      }
      return
    }

    // 添加到待处理文件项
    const newItem = {
      file: file.raw,
      fileName: file.name,
      content: '',
      processed: false,
      valid: true // 添加有效性标记
    }

    // 读取文件内容
    const reader = new FileReader()
    reader.onload = e => {
      const content = e.target.result
      newItem.content = content.trim()

      // 验证CSV文件是否包含必要的列
      const validationResult = validateCsvColumns(newItem.content, newItem.fileName)
      if (!validationResult.isValid) {
        // 标记为无效
        newItem.valid = false
        ElMessage.error(validationResult.message)

        // 从上传组件中移除该文件
        if (uploadRef.value) {
          setTimeout(() => {
            // 使用setTimeout确保DOM更新后再移除文件
            uploadRef.value.handleRemove(file)
          }, 100)
        }
      } else {
        // 标记为已处理
        newItem.processed = true
        // 添加到文件项数组
        importForm.fileItems.push(newItem)
      }
    }
    reader.readAsText(file.raw)
  }
}

// 验证CSV是否包含必要的列
const validateCsvColumns = (content, fileName) => {
  // 确保内容不为空
  if (!content || content.trim() === '') {
    return {
      isValid: false,
      message: `文件 ${fileName} 内容为空`
    }
  }

  // 将内容按行分割
  const lines = content.split('\n').filter(line => line.trim() !== '')

  // 确保至少有标题行
  if (lines.length === 0) {
    return {
      isValid: false,
      message: `文件 ${fileName} 内容为空`
    }
  }

  // 检查标题行是否包含必要的列
  const header = lines[0].toLowerCase()
  const requiredColumns = ['productkey', 'sn', 'secret', 'mac', '设备名称', '项目名称']
  const missingColumns = []

  for (const column of requiredColumns) {
    if (!header.includes(column)) {
      missingColumns.push(column)
    }
  }

  if (missingColumns.length > 0) {
    return {
      isValid: false,
      message: `文件 ${fileName} 缺少必要的列: ${missingColumns.join(', ')}，请重新上传`
    }
  }

  return { isValid: true }
}

// 提交导入
const submitImport = async () => {
  // 过滤只保留有效且处理完成的文件
  const validItems = importForm.fileItems.filter(item => item.valid && item.processed)

  if (validItems.length === 0) {
    ElMessage.warning('请选择要导入的有效文件')
    return
  }

  submitting.value = true
  let successCount = 0
  let failCount = 0

  try {
    // 检查文件名唯一性并导入
    for (const item of validItems) {
      try {
        // 查询是否有同名文件
        const checkParams = {
          file_name: item.fileName,
          page: 1,
          pageSize: 1
        }
        const checkResponse = await getImportList(checkParams)

        if (checkResponse.success && checkResponse.data.list.length > 0) {
          ElMessage.warning(`文件 ${item.fileName} 已存在，请使用唯一的文件名`)
          failCount++
          continue
        }

        // 如果文件名唯一，则执行导入
        const response = await createImport({
          file_name: item.fileName,
          content: item.content
        })

        if (response.success) {
          const createdDevices = response.data.createdDevices || 0
          successCount++
          console.log(`文件 ${item.fileName} 成功导入，创建了 ${createdDevices} 个设备`)
        } else {
          failCount++
          console.error(`文件 ${item.fileName} 导入失败: ${response.message || '未知错误'}`)
        }
      } catch (error) {
        failCount++
        console.error(`文件 ${item.fileName} 导入出错:`, error)
      }
    }

    // 显示导入结果
    if (successCount > 0 && failCount === 0) {
      ElMessage.success(`成功导入 ${successCount} 个文件，设备数据已添加到设备管理中`)
    } else if (successCount > 0 && failCount > 0) {
      ElMessage.warning(`成功导入 ${successCount} 个文件，${failCount} 个文件导入失败`)
    } else {
      ElMessage.error('所有文件导入失败')
    }

    // 关闭对话框
    if (successCount > 0) {
      dialogVisible.value = false

      // 刷新列表
      fetchImportList()
    }
  } catch (error) {
    console.error('导入文件出错:', error)
    ElMessage.error('导入文件失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

// 查看导入详情
const viewImportDetail = row => {
  // 跳转到详情页面，并传递ID参数和文件名
  router.push({
    path: `/import/detail/${row.id}`,
    query: { fileName: row.fileName }
  })
}

// 页码变化
const handleCurrentChange = val => {
  currentPage.value = val
  fetchImportList()
}

// 下载模板
const downloadTemplate = () => {
  // 创建CSV内容，包含所有验证时需要的列
  const templateContent = 'ProductKey,SN,Secret,Mac,设备名称,项目名称\n'

  // 创建Blob对象
  const blob = new Blob([templateContent], { type: 'text/csv;charset=utf-8;' })

  // 创建下载链接
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)

  // 获取当前时间戳
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')

  // 设置下载属性，文件名带时间戳
  link.setAttribute('href', url)
  link.setAttribute('download', `import_template_${timestamp}.csv`)
  link.style.visibility = 'hidden'

  // 添加到文档并触发点击
  document.body.appendChild(link)
  link.click()

  // 清理
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  ElMessage.success('模板下载成功')
}

// 关闭对话框按钮
const handleCloseDialog = () => {
  dialogVisible.value = false
}

onMounted(() => {
  // 初始加载数据
  fetchImportList()
})

// 组件卸载时清理防抖函数
onUnmounted(() => {
  if (debouncedHandleSearch && typeof debouncedHandleSearch.cancel === 'function') {
    debouncedHandleSearch.cancel()
  }
})
</script>

<style scoped>
.import-container {
  padding: 24px;
  background-color: var(--apple-background);
}

/* 整合搜索和列表的卡片 */
.import-list-card {
  margin-bottom: 24px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

/* 按钮区域样式 */
.filter-buttons {
  display: flex;
  gap: 10px;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 导入弹框样式 */
.import-dialog :deep(.el-dialog__body) {
  text-align: center;
}

.upload-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.upload-container :deep(.el-upload) {
  width: 100%;
}

.upload-container :deep(.el-upload-dragger) {
  width: 100%;
}
</style>

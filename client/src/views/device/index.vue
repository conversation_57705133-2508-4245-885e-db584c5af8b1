<template>
  <div class="device-container">
    <!-- 搜索和列表整合在一个卡片中 -->
    <el-card class="device-list-card">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="24">
          <el-col :span="4">
            <el-select
              v-model="filterForm.type"
              placeholder="设备类型"
              clearable
            >
              <el-option
                label="全部"
                value=""
              />
              <el-option
                label="锚杆钻"
                value="AD"
              />
              <el-option
                label="水锤"
                value="WPD"
              />
              <el-option
                label="超前钻机"
                value="PDF"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-input
              v-model="filterForm.name"
              placeholder="设备名称"
              clearable
            />
          </el-col>
          <el-col :span="4">
            <el-input
              v-model="filterForm.serialNumber"
              placeholder="设备序列号"
              clearable
            />
          </el-col>
          <el-col
            :span="12"
            class="filter-buttons"
          >
            <el-button
              type="primary"
              @click="debouncedSearchDevices"
            >
              搜索
            </el-button>
            <el-button @click="resetForm">
              清空
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 设备列表 -->
      <el-table
        v-loading="loading"
        :data="deviceList"
        style="width: 100%"
        border
        stripe
      >
        <el-table-column
          prop="type"
          label="设备类型"
          align="center"
        >
          <template #default="scope">
            <el-tag
              :type="getDeviceTagType(scope.row.type)"
              effect="light"
            >
              {{ scope.row.typeName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="deviceName"
          label="设备名称"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="serialNumber"
          label="设备序列号"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="lastDataTime"
          label="最后数据时间"
          align="center"
        />
        <el-table-column
          prop="lastFileTime"
          label="最后文件上传时间"
          align="center"
        />
        <el-table-column
          label="操作"
          align="center"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="viewDeviceDetail(scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              type="primary"
              link
              @click="viewRealTimeData(scope.row)"
            >
              实时数据
            </el-button>
            <el-button
              type="primary"
              link
              @click="viewDigitalCore(scope.row)"
            >
              数字岩芯
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页部分 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getDeviceList } from '@/api'
import { getDeviceTagType } from '@/utils/utils'
import { debouncedApiCall } from '@/utils/debounce'

const router = useRouter()

// 筛选表单
const filterForm = reactive({
  type: '',
  name: '',
  serialNumber: ''
})

// 设备列表数据
const deviceList = ref([])
const loading = ref(false)
const total = ref(0)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 获取设备列表
const fetchDevices = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 添加筛选条件
    if (filterForm.type) params.type = filterForm.type
    if (filterForm.name) params.name = filterForm.name
    if (filterForm.serialNumber) params.serialNumber = filterForm.serialNumber

    const response = await getDeviceList(params)

    if (response.success) {
      deviceList.value = response.data.list
      total.value = response.data.total
    } else {
      ElMessage.error('获取设备列表失败')
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 搜索设备
const searchDevices = () => {
  currentPage.value = 1
  fetchDevices()
}

// 使用防抖的搜索函数
const debouncedSearchDevices = debouncedApiCall(() => {
  currentPage.value = 1
  fetchDevices()
})

// 组件卸载时清理防抖函数
onUnmounted(() => {
  if (debouncedSearchDevices && typeof debouncedSearchDevices.cancel === 'function') {
    debouncedSearchDevices.cancel()
  }
})

// 重置表单
const resetForm = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = ''
  })
  currentPage.value = 1
  fetchDevices()
}

// 当前页变化
const handleCurrentChange = val => {
  currentPage.value = val
  fetchDevices()
}

// 查看设备详情
const viewDeviceDetail = row => {
  router.push({
    path: `/device/detail/${row.id}`,
    query: { deviceName: row.deviceName }
  })
}

// 查看实时数据
const viewRealTimeData = row => {
  router.push({
    path: `/device/realtime/${row.id}`,
    query: { deviceName: row.deviceName }
  })
}

// 查看数字岩芯
const viewDigitalCore = row => {
  router.push({
    path: `/device/digitalcore/${row.id}`,
    query: { deviceName: row.deviceName }
  })
}

onMounted(() => {
  // 初始加载设备数据
  fetchDevices()
})
</script>

<style scoped>
.device-container {
  padding: 24px;
  background-color: var(--apple-background);
}

/* 整合搜索和列表的卡片 */
.device-list-card {
  margin-bottom: 24px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-buttons {
  text-align: right;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: center;
}

/* 设备类型标签样式 - 与ApplyAlgorithmDialog.vue保持一致 */
.el-tag {
  width: auto;
  min-width: 90px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .filter-buttons {
    text-align: left;
    margin-top: 16px;
  }
}

.el-table :deep(.el-table__cell) {
  padding: 12px 0;
}
</style>

// 确保最早加载环境变量
import dotenv from 'dotenv'
import path from 'path'
// 从当前目录加载.env文件
dotenv.config({ path: path.resolve(process.cwd(), '.env') })

// 初始化console重定向（必须在其他模块导入之前）
import { setupConsoleRedirection } from './middleware/logger'
setupConsoleRedirection()

// 添加全局异常处理器
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
  console.error('应用将在5秒后退出...')
  setTimeout(() => {
    process.exit(1)
  }, 5000)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise rejection:', reason)
  console.error('Promise:', promise)
  console.error('应用将在5秒后退出...')
  setTimeout(() => {
    process.exit(1)
  }, 5000)
})

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，开始优雅关闭...')
  process.exit(0)
})

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，开始优雅关闭...')
  process.exit(0)
})

import app from './app'

const PORT = process.env.PORT || 3001

// 启动服务器
const startServer = async () => {
  try {
    console.log('正在启动服务器...')

    const server = app.listen(PORT, () => {
      console.log(`✅ 服务器成功启动在 http://localhost:${PORT}`)
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`)
      console.log(`📝 日志级别: ${process.env.LOG_LEVEL || 'info'}`)
    })

    // 服务器错误处理
    server.on('error', (error: any) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用`)
      } else {
        console.error('❌ 服务器启动失败:', error)
      }
      process.exit(1)
    })

  } catch (error) {
    console.error('❌ 启动服务器时发生错误:', error)
    process.exit(1)
  }
}

// 启动应用
startServer().catch((error) => {
  console.error('❌ 应用启动失败:', error)
  process.exit(1)
})

<template>
  <div class="device-digitalcore-detail-container page-container">
    <!-- 头部 -->
    <el-page-header
      class="mb-20"
      @back="goBack"
    >
      <template #content>
        <div class="header-content flex-center">
          <span class="device-name">{{ deviceData?.deviceName }}</span>
          <span class="device-title text-primary">数字岩心详情</span>
        </div>
      </template>
    </el-page-header>
    
    <div
      v-loading="loading"
      class="content-wrapper"
    >
      <el-row>
        <!-- 3D显示区域 -->
        <el-col :span="24">
          <div class="content apple-card">
            <WebGLCylinder 
              v-if="coreData"
              :initial-height="coreData ? (coreData.endDepth - coreData.startDepth) / 100 || 10 : 10"
              :initial-diameter="3"
              :initial-image="coreData ? `/images/digital-core/${coreData.imageName}.jpg` : ''"
              :depth-data="queryData"
              :start-depth="coreData?.startDepth || 0"
              :end-depth="coreData?.endDepth || 0"
            />
            <div
              v-else
              class="no-data"
            >
              <el-empty description="暂无3D模型数据" />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import WebGLCylinder from '@/components/WebGLCylinder.vue';
import { ElMessage } from 'element-plus';
import { getDeviceDetail, getDeviceDigitalCoreDetail } from '@/api/device';
import { queryDrillingDataByDepth } from '@/api/data';

export default defineComponent({
  name: 'DigitalCoreDetailView',
  components: {
    WebGLCylinder
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const deviceId = route.params.deviceId as string;
    const fileId = route.params.fileId as string;
    const deviceData = ref<any>(null);
    const coreData = ref<any>(null);
    const loading = ref(true);
    const queryLoading = ref(false);
    const queryData = ref<any[]>([]);
    
    // 查询表单
    const queryForm = reactive({
      deviceId: deviceId,
      holeNo: '',
      startDepth: 0,
      endDepth: 0
    });
    
    // 获取设备数据
    const fetchDeviceData = async () => {
      try {
        loading.value = true;
        const response = await getDeviceDetail(deviceId);
        
        if (response.success && response.data) {
          deviceData.value = response.data;
        } else {
          ElMessage.error('获取设备数据失败');
        }
      } catch (error) {
        ElMessage.error('获取设备数据失败');
        console.error('获取设备数据失败:', error);
      } finally {
        loading.value = false;
      }
    };
    
    // 获取数字岩芯详情数据
    const fetchDigitalCoreDetail = async () => {
      try {
        loading.value = true;
        const response = await getDeviceDigitalCoreDetail(deviceId, fileId);
        
        if (response.success && response.data) {
          coreData.value = response.data;
          // 填充默认的开始和结束深度及孔号
          queryForm.startDepth = coreData.value.startDepth || 0;
          queryForm.endDepth = coreData.value.endDepth || 0;
          queryForm.holeNo = coreData.value.holeNo || '';
          
          // 获取到岩心数据后自动查询原始数据
          await fetchOriginalData();
        } else {
          ElMessage.error('获取数字岩芯详情失败');
        }
      } catch (error) {
        ElMessage.error('获取数字岩芯详情失败');
        console.error('获取数字岩芯详情失败:', error);
      } finally {
        loading.value = false;
      }
    };
    
    // 查询原始数据 (后台获取，不展示)
    const fetchOriginalData = async () => {
      // 如果缺少必要数据，则不查询
      if (!queryForm.holeNo || queryForm.endDepth <= queryForm.startDepth) {
        console.log('缺少查询必要数据或深度范围无效');
        return;
      }
      
      try {
        queryLoading.value = true;
        
        // 使用深度查询接口
        const response = await queryDrillingDataByDepth({
          deviceId: queryForm.deviceId,
          holeNo: queryForm.holeNo,
          startDepth: queryForm.startDepth,
          endDepth: queryForm.endDepth
        });
        
        if (response.success && response.data) {
          queryData.value = response.data || [];
          console.log(`获取到 ${queryData.value.length} 条原始数据`);
        } else {
          console.log('获取原始数据失败:', response.message);
          queryData.value = [];
        }
      } catch (error) {
        console.error('获取原始数据失败:', error);
        queryData.value = [];
      } finally {
        queryLoading.value = false;
      }
    };
    
    // 返回上一页
    const goBack = () => {
      router.back();
    };
    
    onMounted(() => {
      fetchDeviceData();
      fetchDigitalCoreDetail();
    });
    
    return {
      deviceData,
      coreData,
      loading,
      goBack,
      queryData
    };
  }
});
</script>

<style scoped>
.device-digitalcore-detail-container {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.header-content {
  gap: 16px;
}

.device-name {
  font-size: 20px;
  font-weight: 600;
  color: var(--apple-text-primary);
}

.device-title {
  font-size: 14px;
  background-color: rgba(0, 113, 227, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
}

.content-wrapper {
  height: calc(100% - 60px);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content {
  position: relative;
  height: 100%;
  flex: 1;
  overflow: hidden;
  transition: var(--apple-transition);
  display: flex;
  align-items: stretch;
  padding: 0;
}

.el-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
  height: 100%;
}

.el-col {
  padding-left: 0 !important;
  padding-right: 0 !important;
  height: 100%;
}

.core-details {
  height: 100%;
  padding: 16px;
}

.core-details h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
}

.details-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.details-list li {
  display: flex;
  margin-bottom: 12px;
  font-size: 14px;
}

.details-list .label {
  width: 120px;
  color: var(--apple-text-secondary);
}

.details-list .value {
  flex: 1;
  color: var(--apple-text-primary);
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style> 
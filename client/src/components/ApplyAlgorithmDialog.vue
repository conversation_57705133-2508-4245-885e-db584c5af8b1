<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`应用「${algorithmName}」算法`"
    width="65%"
    destroy-on-close
    class="apply-algorithm-dialog"
  >
    <div
      v-loading="loading"
      class="apply-dialog-content"
    >
      <div class="filter-section">
        <el-card
          shadow="hover"
          class="filter-card"
        >
          <div class="filter-header">
            <el-icon><el-icon-filter /></el-icon>
            <span>筛选条件</span>
          </div>
          <el-row :gutter="24">
            <el-col :span="4">
              <el-input
                v-model="searchKey"
                placeholder="设备名称/序列号"
                clearable
                prefix-icon="Search"
                @input="handleSearch"
              />
            </el-col>
            <el-col
              v-if="algorithmType === 1"
              :span="4"
            >
              <el-select
                v-model="selectedProductKey"
                placeholder="设备类型"
                clearable
                style="width: 100%"
                @change="handleSearch"
              >
                <el-option
                  label="锚杆钻"
                  value="AD"
                />
                <el-option
                  label="水锤"
                  value="WPD"
                />
                <el-option
                  label="超前钻机"
                  value="PDF"
                />
              </el-select>
            </el-col>
            <el-col :span="algorithmType === 1 ? 16 : 20">
              <div class="device-count">
                <el-tooltip
                  content="未应用该算法的设备数量"
                  placement="top"
                  effect="light"
                >
                  <el-tag
                    type="info"
                    size="large"
                    effect="plain"
                  >
                    可应用设备数量: {{ deviceList.length }}
                  </el-tag>
                </el-tooltip>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <el-card
        shadow="never"
        class="table-card"
      >
        <el-table
          ref="multipleTable"
          :data="deviceList"
          style="width: 100%"
          height="380px"
          border
          stripe
          highlight-current-row
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            align="center"
          />
          <el-table-column
            prop="type"
            label="设备类型"
            align="center"
          >
            <template #default="scope">
              <el-tag
                :type="getDeviceTagType(scope.row.type)"
                effect="light"
              >
                {{ scope.row.typeName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="type"
            label="设备名称"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.name }}
            </template>
          </el-table-column>
          <el-table-column
            prop="serialNumber"
            label="设备序列号"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            label="当前应用算法"
            align="center"
          >
            <template #default="scope">
              <el-tag
                v-if="getCurrentAlgorithm(scope.row, algorithmType)"
                type="success"
                effect="light"
              >
                {{ getCurrentAlgorithm(scope.row, algorithmType) }}
              </el-tag>
              <span
                v-else
                class="no-algorithm"
              >暂无应用算法</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">
          取 消
        </el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          :disabled="selectedDevices.length === 0"
          @click="handleApply"
        >
          应用到 {{ selectedDevices.length }} 台设备
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, defineProps, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDeviceListWithAlgorithm, batchSetDeviceArithmetic } from '@/api'
import { getDeviceTagType } from '@/utils/utils'
import { debouncedApiCall, debouncedSearch } from '@/utils/debounce'
// 导入图标
import { Filter as ElIconFilter } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  algorithmId: {
    type: Number,
    required: true
  },
  algorithmName: {
    type: String,
    default: ''
  },
  algorithmType: {
    type: Number,
    required: true
  },
  productKey: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 设备列表数据
const deviceList = ref([])
const loading = ref(false)
const submitLoading = ref(false)
const selectedDevices = ref([])
const searchKey = ref('')
const selectedProductKey = ref('')
const dialogVisible = ref(false)

// 监听visible属性变化
watch(
  () => props.visible,
  newVal => {
    dialogVisible.value = newVal
    if (newVal) {
      // 初始化设备类型筛选
      if (props.algorithmType === 1 && props.productKey) {
        selectedProductKey.value = props.productKey
      }
      fetchDevices()
    }
  }
)

// 监听dialogVisible变化
watch(dialogVisible, newVal => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 获取设备列表
const fetchDevices = async () => {
  try {
    loading.value = true
    // 构建查询参数
    const params = {
      page: 1,
      pageSize: 100,
      algorithmType: props.algorithmType, // 传递算法类型参数
      arithmeticId: props.algorithmId // 传递算法ID参数，用于后端过滤
    }

    // 如果是分析算法（控件算法）且有产品类型，则按产品类型筛选
    if (props.algorithmType === 1 && selectedProductKey.value) {
      params.type = selectedProductKey.value
    }

    // 搜索关键词
    if (searchKey.value && searchKey.value.trim().length > 0) {
      // 简单处理，同时搜索设备名称和序列号
      params.name = searchKey.value
      params.serialNumber = searchKey.value
    }

    // 使用新API一次性获取带算法信息的设备列表
    const response = await getDeviceListWithAlgorithm(params)
    if (response.success) {
      console.log('获取到设备列表:', response.data.list)
      deviceList.value = response.data.list || []
    } else {
      ElMessage.error('获取设备列表失败')
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 选择设备变化
const handleSelectionChange = selection => {
  selectedDevices.value = selection.map(item => ({ id: item.id, device_sn: item.device_sn }))
}

// 创建防抖的搜索函数
const debouncedFetchDevices = debouncedApiCall(fetchDevices)

// 搜索设备 - 使用防抖优化
const handleSearch = debouncedFetchDevices

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false
}

// 组件卸载时清理防抖函数
onUnmounted(() => {
  if (debouncedFetchDevices && typeof debouncedFetchDevices.cancel === 'function') {
    debouncedFetchDevices.cancel()
  }
})

// 应用算法到设备
const handleApply = async () => {
  if (selectedDevices.value.length === 0) {
    ElMessage.warning('请选择要应用的设备')
    return
  }

  try {
    submitLoading.value = true

    // 提示确认
    await ElMessageBox.confirm(
      `确定要将算法"${props.algorithmName}"应用到所选的${selectedDevices.value.length}台设备吗？\n` +
        `每台设备只能同时应用一个${props.algorithmType === 0 ? '清洗' : '控件'}算法，此操作可能会替换已有的算法设置。`,
      '确认应用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用批量设置API
    const response = await batchSetDeviceArithmetic({
      deviceIds: selectedDevices.value.map(device => device.id),
      arithmeticId: props.algorithmId,
      arithmeticType: props.algorithmType
    })

    if (response.success) {
      ElMessage.success('应用成功')

      // 刷新设备列表数据，获取最新的算法应用情况
      await fetchDevices()

      emit('success')
      dialogVisible.value = false
    } else {
      ElMessage.error(response.message || '应用失败')
    }
  } catch (error) {
    if (error === 'cancel') return
    console.error('应用算法失败:', error)
    ElMessage.error('应用算法失败: ' + (error.message || '未知错误'))
  } finally {
    submitLoading.value = false
  }
}

// 获取当前应用算法
const getCurrentAlgorithm = (device, type) => {
  // 设备对象中已经包含了应用算法信息
  if (device.algorithms && device.algorithms.length > 0) {
    // 查找指定类型的算法
    const algorithm = device.algorithms.find(item => item.type === type)
    return algorithm ? algorithm.name : null
  }
  return null
}

// 组件挂载时加载数据
onMounted(() => {
  if (props.visible) {
    fetchDevices()
  } else {
    // 确保在visible变为true之前也预加载数据
    fetchDevices()
  }
})
</script>

<style scoped>
.apply-algorithm-dialog :deep(.el-dialog__header) {
  padding: 16px 20px;
  margin-right: 0;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  border-radius: 4px 4px 0 0;
}

.apply-algorithm-dialog :deep(.el-dialog__title) {
  font-weight: bold;
  font-size: 18px;
  color: #303133;
}

.apply-algorithm-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.apply-dialog-content {
  min-height: 450px;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-card {
  margin-bottom: 16px;
}

.filter-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-weight: bold;
  font-size: 15px;
  color: #606266;
}

.filter-header .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.device-count {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  padding-right: 8px;
  text-align: right;
}

.device-count :deep(.el-tag) {
  width: auto;
  min-width: 140px;
  display: inline-flex;
  justify-content: flex-end;
  padding-right: 12px;
}

.table-card {
  margin-bottom: 16px;
}

.no-algorithm {
  color: #909399;
  font-style: italic;
}

.dialog-footer {
  padding: 0 12px;
  text-align: right;
}
</style>

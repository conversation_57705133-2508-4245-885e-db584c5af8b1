import { DataTypes, Model, Optional } from 'sequelize'
import sequelize from '../config/database'

// 导入数据接口
interface ImportAttributes {
  id: number
  created_at: Date
  file_name?: string
  oss_path?: string
  content?: string
  import_rows?: number
}

// 创建时可选的属性
interface ImportCreationAttributes
  extends Optional<ImportAttributes, 'id' | 'file_name' | 'oss_path' | 'content' | 'import_rows'> {}

// 导入数据模型类
class Import extends Model<ImportAttributes, ImportCreationAttributes> implements ImportAttributes {
  public id!: number
  public created_at!: Date
  public file_name?: string
  public oss_path?: string
  public content?: string
  public import_rows?: number
}

// 初始化模型
Import.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '主键ID，自增'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '记录创建时间'
    },
    file_name: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '文件名称'
    },
    oss_path: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '云存储路径（如OSS）'
    },
    import_rows: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '导入文件的行数'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '文件内容'
    }
  },
  {
    sequelize,
    tableName: 'dh_import',
    timestamps: false, // 不使用Sequelize的自动timestamps
    comment: '导入数据表'
  }
)

export default Import

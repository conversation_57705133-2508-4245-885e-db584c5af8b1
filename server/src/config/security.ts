import crypto from 'crypto'

/**
 * 安全配置
 */
export const securityConfig = {
  // JWT配置
  jwt: {
    // 生成强随机密钥的函数
    generateSecret: (): string => {
      return crypto.randomBytes(64).toString('hex')
    },
    
    // JWT选项
    options: {
      algorithm: 'HS256' as const,
      issuer: 'web-panel',
      audience: 'web-panel-client',
      expiresIn: '15m', // 短期访问token
      refreshExpiresIn: '7d' // 长期刷新token
    }
  },

  // 密码策略
  password: {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    specialChars: '@$!%*?&',
    
    // 密码强度验证
    validate: (password: string): { isValid: boolean; errors: string[] } => {
      const errors: string[] = []
      
      if (password.length < securityConfig.password.minLength) {
        errors.push(`密码长度不能少于${securityConfig.password.minLength}个字符`)
      }
      
      if (password.length > securityConfig.password.maxLength) {
        errors.push(`密码长度不能超过${securityConfig.password.maxLength}个字符`)
      }
      
      if (securityConfig.password.requireUppercase && !/[A-Z]/.test(password)) {
        errors.push('密码必须包含至少一个大写字母')
      }
      
      if (securityConfig.password.requireLowercase && !/[a-z]/.test(password)) {
        errors.push('密码必须包含至少一个小写字母')
      }
      
      if (securityConfig.password.requireNumbers && !/\d/.test(password)) {
        errors.push('密码必须包含至少一个数字')
      }
      
      if (securityConfig.password.requireSpecialChars) {
        const specialCharsRegex = new RegExp(`[${securityConfig.password.specialChars}]`)
        if (!specialCharsRegex.test(password)) {
          errors.push(`密码必须包含至少一个特殊字符 (${securityConfig.password.specialChars})`)
        }
      }
      
      return {
        isValid: errors.length === 0,
        errors
      }
    }
  },

  // 文件上传限制
  fileUpload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'text/csv'
    ],
    allowedExtensions: ['.csv'],
    
    // 文件类型验证
    validateFile: (filename: string, mimetype: string): { isValid: boolean; error?: string } => {
      const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'))
      
      if (!securityConfig.fileUpload.allowedExtensions.includes(ext)) {
        return {
          isValid: false,
          error: `不支持的文件类型: ${ext}`
        }
      }
      
      if (!securityConfig.fileUpload.allowedMimeTypes.includes(mimetype)) {
        return {
          isValid: false,
          error: `不支持的MIME类型: ${mimetype}`
        }
      }
      
      return { isValid: true }
    }
  },

  // 输入验证
  input: {
    maxStringLength: 1000,
    maxArrayLength: 100,
    maxObjectDepth: 5,
    
    // 危险字符模式
    dangerousPatterns: [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, // Script标签
      /javascript:/gi, // JavaScript协议
      /on\w+\s*=/gi, // 事件处理器
      /eval\s*\(/gi, // eval函数
      /expression\s*\(/gi, // CSS expression
      /vbscript:/gi, // VBScript协议
      /data:text\/html/gi // Data URL HTML
    ],
    
    // XSS检测
    containsXSS: (input: string): boolean => {
      return securityConfig.input.dangerousPatterns.some(pattern => pattern.test(input))
    },
    
    // 清理输入
    sanitize: (input: string): string => {
      return input
        .trim()
        .replace(/[<>]/g, '') // 移除尖括号
        .replace(/javascript:/gi, '') // 移除JavaScript协议
        .replace(/on\w+=/gi, '') // 移除事件处理器
        .replace(/eval\s*\(/gi, '') // 移除eval
    }
  },

  // 算法执行安全
  algorithm: {
    maxExecutionTime: 30000, // 30秒
    maxMemoryUsage: 128 * 1024 * 1024, // 128MB
    maxCodeSize: 1024 * 1024, // 1MB
    
    // 禁止的API和模块
    forbiddenAPIs: [
      'require',
      'import',
      'eval',
      'Function',
      'setTimeout',
      'setInterval',
      'process',
      'global',
      'Buffer',
      'fs',
      'path',
      'os',
      'child_process',
      'cluster',
      'crypto',
      'http',
      'https',
      'net',
      'dgram',
      'dns'
    ],
    
    // 检查代码是否包含禁止的API
    containsForbiddenAPI: (code: string): { isValid: boolean; forbiddenAPIs: string[] } => {
      const found: string[] = []

      securityConfig.algorithm.forbiddenAPIs.forEach(api => {
        // 特殊处理Function - 只检测构造函数调用，不检测function关键字
        if (api === 'Function') {
          // 检测 new Function() 或 Function() 调用
          const functionCallRegex = /(?:new\s+Function\s*\(|Function\s*\()/gi
          if (functionCallRegex.test(code)) {
            found.push(api)
          }
        } else {
          // 对其他API使用原来的检查方式
          const regex = new RegExp(`\\b${api}\\b`, 'gi')
          if (regex.test(code)) {
            found.push(api)
          }
        }
      })

      return {
        isValid: found.length === 0,
        forbiddenAPIs: found
      }
    }
  },

  // 会话管理
  session: {
    maxConcurrentSessions: 5, // 每个用户最多5个并发会话
    sessionTimeout: 15 * 60 * 1000, // 15分钟无活动超时
    refreshTokenRotation: true, // 启用刷新token轮换
    
    // 生成会话ID
    generateSessionId: (): string => {
      return crypto.randomBytes(32).toString('hex')
    }
  },

  // 审计日志
  audit: {
    logSensitiveOperations: true,
    sensitiveOperations: [
      'login',
      'logout',
      'register',
      'password_change',
      'algorithm_create',
      'algorithm_update',
      'algorithm_delete',
      'device_create',
      'device_update',
      'device_delete',
      'file_upload',
      'file_delete'
    ],
    
    // 记录审计日志
    log: (operation: string, userId: string, details: any, req: any): void => {
      if (securityConfig.audit.sensitiveOperations.includes(operation)) {
        console.log('AUDIT LOG:', {
          timestamp: new Date().toISOString(),
          operation,
          userId,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          details: JSON.stringify(details)
        })
      }
    }
  }
}

/**
 * 生成强随机密码
 */
export const generateSecurePassword = (length: number = 16): string => {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@$!%*?&'
  let password = ''
  
  for (let i = 0; i < length; i++) {
    password += charset.charAt(crypto.randomInt(0, charset.length))
  }
  
  return password
}

/**
 * 生成安全的JWT密钥
 */
export const generateJWTSecret = (): string => {
  return securityConfig.jwt.generateSecret()
}

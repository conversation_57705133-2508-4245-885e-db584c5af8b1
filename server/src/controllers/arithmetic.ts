import { Response, NextFunction, Request } from 'express'
import { Arithmetic } from '../models'
import { AppError } from '../middleware/error'
import { Op } from 'sequelize'
import crypto from 'crypto'
import { uploadAlgorithmsContentToOSS, deleteFileFromOSS } from '../utils/oss'
import { getArithmeticDeviceCount } from './deviceArithmetic'
import { getDeviceTypeName, getAlgorithmTypeName, formatDateTime } from '../utils/utils'
import Device from '../models/device'
import { AlgorithmExecutor } from '../utils/algorithmExecutor'
import { CodeSecurityAnalyzer } from '../utils/codeSecurityAnalyzer'

// 定义查询参数接口
interface ArithmeticQueryParams {
  type?: string
  product_key?: string
  name?: string
  is_default?: string
  page?: string
  pageSize?: string
}

// 算法执行器实例
const algorithmExecutor = new AlgorithmExecutor()

/**
 * 获取算法列表
 */
export const getArithmeticList = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const {
      type,
      product_key,
      name,
      is_default,
      page = '1',
      pageSize = '10'
    } = req.query as ArithmeticQueryParams

    // 建立查询条件
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {}

    // 根据算法类型过滤
    if (type !== undefined && type !== '') {
      where.type = type
    }

    // 根据产品类型过滤
    if (product_key) {
      where.product_key = product_key
    }

    // 根据名称过滤
    if (name) {
      where.name = { [Op.like]: `%${name}%` }
    }

    // 根据是否默认过滤
    if (is_default !== undefined && is_default !== '') {
      where.is_default = is_default
    }

    // 分页设置
    const pageNumber = parseInt(page)
    const limit = parseInt(pageSize)
    const offset = (pageNumber - 1) * limit

    // 查询算法列表
    const { count, rows } = await Arithmetic.findAndCountAll({
      where,
      limit,
      offset,
      order: [['created_at', 'DESC']]
    })

    // 获取每个算法的应用设备数量
    const arithmeticDeviceCounts = await Promise.all(
      rows.map(async arithmetic => ({
        id: arithmetic.id,
        count: await getArithmeticDeviceCount(arithmetic.id)
      }))
    )

    // 转换为前端需要的格式
    const arithmetics = rows.map(arithmetic => {
      // 使用工具函数获取算法类型名称
      const typeName = getAlgorithmTypeName(arithmetic.type)

      // 使用工具函数获取设备类型名称
      const productName = getDeviceTypeName(arithmetic.product_key)

      // 获取当前算法的应用设备数量
      const deviceCount = arithmeticDeviceCounts.find(item => item.id === arithmetic.id)?.count || 0

      return {
        id: arithmetic.id,
        type: arithmetic.type,
        typeName: typeName,
        name: arithmetic.name,
        productKey: arithmetic.product_key,
        productName: productName,
        isDefault: arithmetic.is_default,
        createdAt: formatDateTime(arithmetic.created_at),
        modifiedAt: formatDateTime(arithmetic.modified_at),
        usedDeviceCount: deviceCount
      }
    })

    res.status(200).json({
      success: true,
      data: {
        total: count,
        list: arithmetics
      }
    })
  } catch (error) {
    console.error('获取算法列表失败:', error)
    next(error)
  }
}

/**
 * 获取算法详情
 */
export const getArithmeticDetail = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params

    const arithmetic = await Arithmetic.findByPk(id)

    if (!arithmetic) {
      throw new AppError('算法不存在', 404)
    }

    // 使用工具函数获取算法类型名称
    const typeName = getAlgorithmTypeName(arithmetic.type)

    // 使用工具函数获取设备类型名称
    const productName = getDeviceTypeName(arithmetic.product_key)

    res.status(200).json({
      success: true,
      data: {
        id: arithmetic.id,
        type: arithmetic.type,
        typeName: typeName,
        name: arithmetic.name,
        productKey: arithmetic.product_key,
        productName: productName,
        ossPath: arithmetic.oss_path,
        content: arithmetic.content,
        md5: arithmetic.md5,
        isDefault: arithmetic.is_default,
        createdAt: formatDateTime(arithmetic.created_at),
        modifiedAt: formatDateTime(arithmetic.modified_at)
      }
    })
  } catch (error) {
    console.error('获取算法详情失败:', error)
    next(error)
  }
}

/**
 * 创建算法
 */
export const createArithmetic = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { type, name, product_key, content, md5, oss_path, is_default } = req.body
    // 验证必填字段
    if (!name || type === undefined || type === null) {
      throw new AppError('缺少必填参数', 400)
    }

    // 构建查询条件，根据算法类型决定是否需要product_key
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const whereCondition: any = { name }

    // 只有在分析算法(type=1)时才将product_key加入查询条件
    // 对于清洗算法(type=0)，名称全局唯一，不需要按product_key区分
    if (type === 1 && product_key) {
      whereCondition.product_key = product_key
    }

    // 检查算法名称是否已存在
    const existingAlgorithm = await Arithmetic.findOne({
      where: whereCondition
    })

    if (existingAlgorithm) {
      throw new AppError(`算法名称"${name}"已存在，请使用其他名称`, 400)
    }

    // 如果设置为默认算法，需要将同类型的默认算法取消默认状态
    if (is_default) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const defaultWhereCondition: any = { type, is_default: true }

      // 只有分析算法才需要按产品类型区分默认算法
      if (type === 1 && product_key) {
        defaultWhereCondition.product_key = product_key
      }

      await Arithmetic.update(
        { is_default: false },
        {
          where: defaultWhereCondition
        }
      )
    }

    // 如果有算法内容，先进行安全检测
    if (content) {
      console.log('开始对新算法进行安全检测...')
      const securityAnalyzer = new CodeSecurityAnalyzer()
      const securityResult = securityAnalyzer.analyzeCode(content)

      if (!securityResult.isSecure) {
        const criticalViolations = securityResult.violations.filter(v => v.severity === 'CRITICAL')
        const highViolations = securityResult.violations.filter(v => v.severity === 'HIGH')

        if (criticalViolations.length > 0 || securityResult.riskLevel === 'CRITICAL') {
          const errorMessage = `算法代码包含严重安全风险，无法保存。请移除代码中的危险操作，如eval、Function等危险函数。`
          throw new AppError(errorMessage, 400)
        }

        if (highViolations.length > 0 || securityResult.riskLevel === 'HIGH') {
          const errorMessage = `算法代码包含高安全风险，无法保存。请修复代码中的高风险问题，避免访问危险属性。`
          throw new AppError(errorMessage, 400)
        }

        // 记录中低风险违规但允许保存
        if (securityResult.violations.length > 0) {
          console.warn(`算法代码安全警告 (风险等级: ${securityResult.riskLevel}, 安全分数: ${securityResult.score}):`,
            securityResult.violations.map(v => v.message))
        }
      }

      console.log(`算法安全检测通过 - 安全分数: ${securityResult.score}, 风险等级: ${securityResult.riskLevel}`)
    }

    // 处理算法内容上传到OSS
    let ossPathValue = oss_path
    let md5Value = md5

    if (content && !oss_path) {
      try {
        // 计算内容的MD5值
        md5Value = crypto.createHash('md5').update(content).digest('hex')

        // 上传内容到OSS，传递算法类型和产品类型
        ossPathValue = await uploadAlgorithmsContentToOSS(content, name, type, product_key)
      } catch (ossError) {
        console.error('上传到OSS失败:', ossError)
        // 上传失败不影响后续操作，但记录错误
      }
    }

    // 创建新算法
    const arithmetic = await Arithmetic.create({
      type: type || 0,
      name,
      product_key, // 对于清洗算法，这可能是undefined
      content,
      oss_path: ossPathValue,
      md5: md5Value,
      is_default: is_default || false,
      created_at: new Date(),
      modified_at: new Date()
    })

    res.status(201).json({
      success: true,
      data: arithmetic
    })
  } catch (error) {
    console.error('创建算法失败:', error)
    next(error)
  }
}

/**
 * 更新算法
 */
export const updateArithmetic = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { type, name, product_key, content, oss_path, is_default } = req.body

    const arithmetic = await Arithmetic.findByPk(id)

    if (!arithmetic) {
      throw new AppError('算法不存在', 404)
    }
    
    // 在更新前清除该算法的缓存,确保更新后会重新加载
    algorithmExecutor.clearCache(Number(id))

    // 检查名称是否变化
    const isNameChanged = name && name !== arithmetic.name

    // 如果名称或产品类型发生变化，检查是否与现有算法重名
    if (isNameChanged || (product_key && product_key !== arithmetic.product_key)) {
      const newName = name || arithmetic.name
      const currentType = type !== undefined ? type : arithmetic.type

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const whereCondition: any = {
        name: newName,
        id: { [Op.ne]: id } // 排除当前算法
      }

      // 只有分析算法才需要按产品类型区分
      if (currentType === 1) {
        const newProductKey = product_key !== undefined ? product_key : arithmetic.product_key
        if (newProductKey) {
          whereCondition.product_key = newProductKey
        }
      }

      const existingAlgorithm = await Arithmetic.findOne({
        where: whereCondition
      })

      if (existingAlgorithm) {
        throw new AppError(`算法名称"${newName}"已存在，请使用其他名称`, 400)
      }
    }

    // 如果要设置为默认算法，需要将同类型的默认算法取消默认状态
    if (is_default && !arithmetic.is_default) {
      const currentType = type !== undefined ? type : arithmetic.type

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const defaultWhereCondition: any = {
        type: currentType,
        is_default: true,
        id: { [Op.ne]: id } // 排除当前算法
      }

      // 只有分析算法才需要按产品类型区分默认算法
      if (currentType === 1) {
        const currentProductKey = product_key !== undefined ? product_key : arithmetic.product_key
        if (currentProductKey) {
          defaultWhereCondition.product_key = currentProductKey
        }
      }

      await Arithmetic.update(
        { is_default: false },
        {
          where: defaultWhereCondition
        }
      )
    }

    // 准备更新的数据
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updateData: any = {
      type: type !== undefined ? type : arithmetic.type,
      name: name || arithmetic.name,
      product_key: product_key !== undefined ? product_key : arithmetic.product_key,
      oss_path: oss_path !== undefined ? oss_path : arithmetic.oss_path,
      is_default: is_default !== undefined ? is_default : arithmetic.is_default,
      modified_at: new Date()
    }

    // 当前算法类型和产品类型（可能已更新）
    const currentType = updateData.type
    const currentProductKey = updateData.product_key

    // 只有当content传入时才进行MD5校验和OSS上传
    if (content !== undefined) {
      // 先进行安全检测
      console.log('开始对更新的算法进行安全检测...')
      const securityAnalyzer = new CodeSecurityAnalyzer()
      const securityResult = securityAnalyzer.analyzeCode(content)

      if (!securityResult.isSecure) {
        const criticalViolations = securityResult.violations.filter(v => v.severity === 'CRITICAL')
        const highViolations = securityResult.violations.filter(v => v.severity === 'HIGH')

        if (criticalViolations.length > 0 || securityResult.riskLevel === 'CRITICAL') {
          const errorMessage = `算法代码包含严重安全风险，无法更新。请移除代码中的危险操作，如eval、Function等危险函数。`
          throw new AppError(errorMessage, 400)
        }

        if (highViolations.length > 0 || securityResult.riskLevel === 'HIGH') {
          const errorMessage = `算法代码包含高安全风险，无法更新。请修复代码中的高风险问题，避免访问危险属性。`
          throw new AppError(errorMessage, 400)
        }

        // 记录中低风险违规但允许更新
        if (securityResult.violations.length > 0) {
          console.warn(`算法代码安全警告 (风险等级: ${securityResult.riskLevel}, 安全分数: ${securityResult.score}):`,
            securityResult.violations.map(v => v.message))
        }
      }

      console.log(`算法安全检测通过 - 安全分数: ${securityResult.score}, 风险等级: ${securityResult.riskLevel}`)

      // 计算新内容的MD5值
      const newMd5 = crypto.createHash('md5').update(content).digest('hex')

      // 内容有变化，或者名称有变化，或者OSS路径为空，或类型/产品类型有变化，需要重新上传
      const isTypeChanged = type !== undefined && type !== arithmetic.type
      const isProductKeyChanged =
        product_key !== undefined && product_key !== arithmetic.product_key

      if (
        newMd5 !== arithmetic.md5 ||
        isNameChanged ||
        arithmetic.oss_path === null ||
        isTypeChanged ||
        isProductKeyChanged
      ) {
        updateData.content = content
        updateData.md5 = newMd5

        try {
          // 记录旧的OSS路径，用于后续删除
          const oldOssPath = arithmetic.oss_path

          // 当内容或名称或类型变化时，上传到OSS
          // 使用算法名作为文件名前缀，并传递当前类型和产品类型
          const algorithmName = updateData.name || 'algorithm'
          const ossPath = await uploadAlgorithmsContentToOSS(
            content,
            algorithmName,
            currentType,
            currentProductKey
          )

          // 设置新的oss_path
          updateData.oss_path = ossPath

          // 如果旧路径存在，则删除旧文件
          if (oldOssPath) {
            try {
              await deleteFileFromOSS(oldOssPath)
              console.log('成功删除旧文件:', oldOssPath)
            } catch (deleteError) {
              console.error('删除旧文件失败:', deleteError)
              // 删除失败不影响更新过程
            }
          }
        } catch (ossError) {
          console.error('上传到OSS失败:', ossError)
          // 上传失败不影响后续操作，但记录错误
        }
      } else {
        console.log('算法内容和名称未变化，跳过内容更新和OSS上传')
      }
    } else if (
      (isNameChanged || type !== undefined || product_key !== undefined) &&
      arithmetic.content
    ) {
      // 当仅名称或类型或产品类型变化但没有传入内容时，使用现有内容重新上传
      try {
        // 记录旧的OSS路径，用于后续删除
        const oldOssPath = arithmetic.oss_path

        // 使用新名称重新上传现有内容，并传递当前类型和产品类型
        const algorithmName = updateData.name || 'algorithm'
        const ossPath = await uploadAlgorithmsContentToOSS(
          arithmetic.content,
          algorithmName,
          currentType,
          currentProductKey
        )

        // 设置新的oss_path
        updateData.oss_path = ossPath

        // 如果旧路径存在，则删除旧文件
        if (oldOssPath) {
          try {
            await deleteFileFromOSS(oldOssPath)
            console.log('成功删除旧文件:', oldOssPath)
          } catch (deleteError) {
            console.error('删除旧文件失败:', deleteError)
            // 删除失败不影响更新过程
          }
        }
      } catch (ossError) {
        console.error('使用新名称或类型重新上传到OSS失败:', ossError)
        // 上传失败不影响后续操作，但记录错误
      }
    }

    // 更新算法信息
    await arithmetic.update(updateData)

    // 预加载算法
    try {
      await algorithmExecutor.loadAlgorithm(arithmetic)
      console.log('算法预加载成功')
    } catch (loadError) {
      console.error('算法预加载失败:', loadError)
      // 预加载失败不影响更新过程
    }

    res.status(200).json({
      success: true,
      data: arithmetic
    })
  } catch (error) {
    console.error('更新算法失败:', error)
    next(error)
  }
}

/**
 * 删除算法
 */
export const deleteArithmetic = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params

    const arithmetic = await Arithmetic.findByPk(id)

    if (!arithmetic) {
      throw new AppError('算法不存在', 404)
    }

    // 默认算法不能删除
    if (arithmetic.is_default) {
      throw new AppError('默认算法不能删除', 400)
    }
    
    // 清除算法缓存
    algorithmExecutor.clearCache(Number(id))
    console.log(`删除算法前清除缓存: ${id}`)

    // 获取OSS路径，如果存在则删除OSS文件
    if (arithmetic.oss_path) {
      try {
        const deleteResult = await deleteFileFromOSS(arithmetic.oss_path)
        if (deleteResult) {
          console.log('成功删除OSS文件:', arithmetic.oss_path)
        } else {
          console.warn('删除OSS文件失败:', arithmetic.oss_path)
        }
      } catch (ossError) {
        console.error('删除OSS文件出错:', ossError)
        // 删除OSS文件失败不应影响算法记录的删除，所以这里只记录错误但继续执行
      }
    }

    // 删除算法数据库记录
    await arithmetic.destroy()

    res.status(200).json({
      success: true,
      message: '算法删除成功'
    })
  } catch (error) {
    console.error('删除算法失败:', error)
    next(error)
  }
}

/**
 * 下载算法
 */
export const downloadArithmetic = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { deviceId } = req.query

    if (!deviceId) {
      throw new AppError('缺少设备ID参数', 400)
    }

    // 查询算法
    const arithmetic = await Arithmetic.findByPk(id)

    if (!arithmetic) {
      throw new AppError('算法不存在', 404)
    }

    // 查询设备信息，获取MAC地址和Secret
    const device = await Device.findByPk(deviceId as string)

    if (!device) {
      throw new AppError('设备不存在', 404)
    }

    if (!device.device_mac || !device.device_secret) {
      throw new AppError('设备信息不完整，无法生成加密密钥', 400)
    }

    // 获取MAC地址后6位
    const macLastSix = device.device_mac.slice(-6)

    // 生成加密密钥 = deviceMac后6位 + deviceSecret
    const encryptKey = macLastSix + device.device_secret

    // 确保密钥为16位（AES-128), 24位(AES-192)或32位(AES-256)
    let finalKey = encryptKey
    if (encryptKey.length > 32) {
      finalKey = encryptKey.slice(0, 32)
    } else if (encryptKey.length > 24) {
      finalKey = encryptKey.slice(0, 24)
    } else if (encryptKey.length > 16) {
      finalKey = encryptKey.slice(0, 16)
    } else {
      // 如果不足16位，补齐到16位
      finalKey = encryptKey.padEnd(16, '0')
    }

    // 使用AES-ECB模式加密
    const cipher = crypto.createCipheriv('aes-128-ecb', finalKey, null)
    cipher.setAutoPadding(true) // 使用PKCS5Padding

    let encryptedContent = ''
    if (arithmetic.content) {
      encryptedContent = Buffer.concat([
        cipher.update(arithmetic.content, 'utf8'),
        cipher.final()
      ]).toString('base64')
    } else {
      throw new AppError('算法内容为空', 400)
    }

    // 返回加密后的内容
    res.status(200).json({
      success: true,
      data: {
        name: arithmetic.name,
        encryptedContent: encryptedContent,
        type: arithmetic.type
      }
    })
  } catch (error) {
    console.error('下载算法失败:', error)
    next(error)
  }
}

/**
 * 执行算法方法
 */
export const executeAlgorithmMethod = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { methodName, params } = req.body

    if (!methodName) {
      throw new AppError('缺少方法名', 400)
    }

    // 获取算法
    const algorithm = await Arithmetic.findByPk(id)
    if (!algorithm) {
      throw new AppError('算法不存在', 404)
    }

    try {
      // 加载算法
      await algorithmExecutor.loadAlgorithm(algorithm)

      // 执行方法前检查方法是否存在
      const methods = await algorithmExecutor.getExposedMethods()
      if (!methods.includes(methodName)) {
        throw new AppError(`算法未提供方法: ${methodName}`, 400)
      }

      // 执行方法
      const result = await algorithmExecutor.executeMethod(methodName, params)

      res.status(200).json({
        success: true,
        data: result
      })
    } catch (error: any) {
      console.error(`执行算法 ${id} 的方法 ${methodName} 失败:`, error)
      throw new AppError(`执行方法失败: ${error.message}`, 400)
    }
  } catch (error) {
    console.error('执行算法方法失败:', error)
    next(error)
  }
}

/**
 * 获取算法暴露的方法列表
 */
export const getAlgorithmMethods = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params

    // 获取算法
    const algorithm = await Arithmetic.findByPk(id)
    if (!algorithm) {
      throw new AppError('算法不存在', 404)
    }

    // 检查算法内容是否为空
    if (!algorithm.content || algorithm.content.trim() === '') {
      console.warn(`算法 ${id} 内容为空，返回空方法列表`);
      res.status(200).json({
        success: true,
        data: []
      });
      return;
    }

    try {
      // 加载算法
      await algorithmExecutor.loadAlgorithm(algorithm)

      // 获取方法列表
      const methods = await algorithmExecutor.getExposedMethods()

      res.status(200).json({
        success: true,
        data: Array.isArray(methods) ? methods : []
      })
    } catch (error) {
      console.error(`获取算法 ${id} 方法列表失败:`, error);
      // 即使获取方法失败，也返回成功状态和空数组，避免前端报错
      res.status(200).json({
        success: true,
        data: []
      });
    }
  } catch (error) {
    console.error('获取算法方法列表失败:', error)
    next(error)
  }
}

/**
 * 获取算法运行状态
 */
export const getAlgorithmStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params

    // 如果提供了算法ID，先尝试加载算法
    if (id) {
      const algorithm = await Arithmetic.findByPk(id)
      if (!algorithm) {
        throw new AppError('算法不存在', 404)
      }
      
      // 加载算法
      await algorithmExecutor.loadAlgorithm(algorithm)
    }

    // 获取算法状态
    const status = algorithmExecutor.getAlgorithmStatus()
    
    res.status(200).json({
      success: true,
      data: status
    })
  } catch (error) {
    console.error('获取算法状态失败:', error)
    next(error)
  }
}

/**
 * 执行多个算法方法
 */
export const executeMultipleAlgorithmMethods = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { methods } = req.body

    if (!methods || !Array.isArray(methods)) {
      throw new AppError('缺少方法列表或格式不正确', 400)
    }

    // 获取算法
    const algorithm = await Arithmetic.findByPk(id)
    if (!algorithm) {
      throw new AppError('算法不存在', 404)
    }

    try {
      // 加载算法(只需要加载一次)
      await algorithmExecutor.loadAlgorithm(algorithm)
      
      // 获取可用方法列表
      const availableMethods = await algorithmExecutor.getExposedMethods()
      
      // 验证所有请求的方法是否存在
      for (const method of methods) {
        if (!method.methodName) {
          throw new AppError('某个方法缺少方法名', 400)
        }
        if (!availableMethods.includes(method.methodName)) {
          throw new AppError(`算法未提供方法: ${method.methodName}`, 400)
        }
      }

      // 依次执行每个方法
      const results = []
      for (const method of methods) {
        const result = await algorithmExecutor.executeMethod(
          method.methodName, 
          method.params || {}
        )
        results.push({
          methodName: method.methodName,
          result
        })
      }

      res.status(200).json({
        success: true,
        data: results
      })
    } catch (error: any) {
      console.error(`执行算法 ${id} 的多个方法失败:`, error)
      throw new AppError(`执行方法失败: ${error.message}`, 400)
    }
  } catch (error) {
    console.error('执行多个算法方法失败:', error)
    next(error)
  }
}

/**
 * 链式执行算法方法
 * 将上一个方法的返回结果作为下一个方法的输入参数
 */
export const executeChainedAlgorithmMethods = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { id } = req.params
    const { methods, params } = req.body

    if (!methods || !Array.isArray(methods) || methods.length === 0) {
      throw new AppError('缺少方法列表或格式不正确', 400)
    }

    // 获取算法
    const algorithm = await Arithmetic.findByPk(id)
    if (!algorithm) {
      throw new AppError('算法不存在', 404)
    }

    try {
      // 加载算法(只需要加载一次)
      await algorithmExecutor.loadAlgorithm(algorithm)
      
      // 获取可用方法列表
      const availableMethods = await algorithmExecutor.getExposedMethods()
      
      // 验证所有请求的方法是否存在
      for (const methodName of methods) {
        if (!methodName) {
          throw new AppError('某个方法名为空', 400)
        }
        if (!availableMethods.includes(methodName)) {
          throw new AppError(`算法未提供方法: ${methodName}`, 400)
        }
      }

      // 链式执行方法
      let currentParams = params || {}
      let result = null
      
      for (const methodName of methods) {
        result = await algorithmExecutor.executeMethod(methodName, currentParams)
        currentParams = result // 将当前方法的结果作为下一个方法的输入参数
      }

      // 只返回最后一个方法的执行结果
      res.status(200).json({
        success: true,
        data: result
      })
    } catch (error: any) {
      console.error(`链式执行算法 ${id} 的方法失败:`, error)
      throw new AppError(`执行方法失败: ${error.message}`, 400)
    }
  } catch (error) {
    console.error('链式执行算法方法失败:', error)
    next(error)
  }
}

/**
 * 分析算法代码安全性
 */
export const analyzeCodeSecurity = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params
    const { code } = req.body

    // 如果提供了算法ID，从数据库获取代码
    let codeToAnalyze = code
    if (id && !code) {
      const algorithm = await Arithmetic.findByPk(id)
      if (!algorithm) {
        throw new AppError('算法不存在', 404)
      }
      codeToAnalyze = algorithm.content
    }

    if (!codeToAnalyze) {
      throw new AppError('请提供要分析的代码', 400)
    }

    // 创建安全分析器实例
    const analyzer = new CodeSecurityAnalyzer()

    // 执行安全分析
    const analysisResult = analyzer.analyzeCode(codeToAnalyze)

    // 返回分析结果
    res.status(200).json({
      success: true,
      data: {
        isSecure: analysisResult.isSecure,
        riskLevel: analysisResult.riskLevel,
        score: analysisResult.score,
        violations: analysisResult.violations,
        summary: {
          totalViolations: analysisResult.violations.length,
          criticalCount: analysisResult.violations.filter(v => v.severity === 'CRITICAL').length,
          highCount: analysisResult.violations.filter(v => v.severity === 'HIGH').length,
          mediumCount: analysisResult.violations.filter(v => v.severity === 'MEDIUM').length,
          lowCount: analysisResult.violations.filter(v => v.severity === 'LOW').length
        },
        recommendations: generateSecurityRecommendations(analysisResult)
      }
    })
  } catch (error) {
    console.error('代码安全分析失败:', error)
    next(error)
  }
}

/**
 * 生成安全建议
 */
function generateSecurityRecommendations(analysisResult: any): string[] {
  const recommendations: string[] = []

  if (analysisResult.violations.length === 0) {
    recommendations.push('代码通过了所有安全检查，可以安全执行')
    return recommendations
  }

  const violationTypes = new Set(analysisResult.violations.map((v: any) => v.type))

  if (violationTypes.has('FORBIDDEN_IDENTIFIER')) {
    recommendations.push('移除代码中的禁止标识符，如eval、Function、require等')
  }

  if (violationTypes.has('FORBIDDEN_PROPERTY')) {
    recommendations.push('避免访问危险属性，如constructor、__proto__等')
  }

  if (violationTypes.has('DYNAMIC_FORBIDDEN_ACCESS')) {
    recommendations.push('不要通过动态属性访问来绕过安全限制')
  }

  if (violationTypes.has('DANGEROUS_PATTERN')) {
    recommendations.push('移除可能用于代码注入的危险模式')
  }

  if (violationTypes.has('CONSTRUCTOR_CALL')) {
    recommendations.push('避免通过constructor访问构造函数')
  }

  if (violationTypes.has('GLOBAL_MODIFICATION')) {
    recommendations.push('不要尝试修改全局对象')
  }

  if (violationTypes.has('STRING_CONCAT_INJECTION')) {
    recommendations.push('检查字符串拼接是否可能构造危险代码')
  }

  if (analysisResult.riskLevel === 'CRITICAL' || analysisResult.riskLevel === 'HIGH') {
    recommendations.push('代码存在严重安全风险，建议重新编写')
  } else if (analysisResult.riskLevel === 'MEDIUM') {
    recommendations.push('代码存在中等安全风险，建议修复后使用')
  }

  return recommendations
}

import { Request, Response, NextFunction } from 'express'

export interface JwtPayload {
  id: string
  username: string
  role: string
  jti?: string
  iat?: number
  exp?: number
}

export interface AuthRequest extends Request {
  user?: JwtPayload
  headers: {
    authorization?: string
  }
  cookies?: {
    token?: string
  }
  // 支持Cookie和Header两种方式传递token
}

export type AuthMiddleware = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => void | Promise<void>

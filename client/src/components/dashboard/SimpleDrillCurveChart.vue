<template>
  <el-card
    class="simple-drill-curve-chart-card"
    shadow="hover"
  >
    <template #header>
      <div class="sub-card-header">
        <span>钻进曲线监控</span>
      </div>
    </template>
    <div
      ref="simpleDrillCurveChart"
      class="simple-drill-curve-chart"
    />
  </el-card>
</template>

<script>
import { ref, watch, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { debouncedChartUpdate, debouncedResize } from '@/utils/debounce'

export default {
  name: 'SimpleDrillCurveChart',
  props: {
    drillingData: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const simpleDrillCurveChart = ref(null)
    let chartInstance = null

    let lastCalculatedHeight = 0 // 记录上次计算的高度，避免无限循环
    let resizeTimer = null // 防抖定时器

    // 常量定义
    const CHART_CONSTANTS = {
      GRID_COUNT: 5,
      COLORS: {
        NORMAL: '#389E0D'
      }
    }

    // 曲线配置
    const curveConfigs = [
      { name: '钻进速度', field: 'advncSpd', unit: 'm/min', color: '#389E0D' },
      { name: '旋转扭矩', field: 'rtnTq', unit: 'Nm', color: '#1890FF' },
      { name: '水压力', field: 'wtrPrsH', unit: 'MPa', color: '#52C41A' },
      { name: '旋转速度', field: 'rtnSpd', unit: 'rpm', color: '#FA8C16' },
      { name: '推进力', field: 'frcstKn', unit: 'kN', color: '#EB2F96' }
    ]

    // 数据处理函数
    const processData = (data) => {
      if (!data || !Array.isArray(data) || data.length === 0) {
        return {
          timeData: [],
          advncSpdData: [],
          rtnTqData: [],
          wtrPrsHData: [],
          rtnSpdData: [],
          frcstKnData: []
        }
      }

      const timeData = []
      const advncSpdData = []
      const rtnTqData = []
      const wtrPrsHData = []
      const rtnSpdData = []
      const frcstKnData = []

      data.forEach(item => {
        // 使用采集时间作为X轴
        const timeValue = item.collectionAt || item.Collection_At || new Date().toISOString()
        timeData.push(timeValue)

        // 处理各个数据字段 - 兼容不同的字段名格式
        advncSpdData.push([timeValue, Number(item.advncSpd || item.Advnc_Spd) || 0])
        rtnTqData.push([timeValue, Number(item.rtnTq || item.Rtn_Tq) || 0])
        wtrPrsHData.push([timeValue, Number(item.wtrPrsH || item.Wtr_Prs_H) || 0])
        rtnSpdData.push([timeValue, Number(item.rtnSpd || item.Rtn_Spd) || 0])
        frcstKnData.push([timeValue, Number(item.frcstKn || item.Frcst_kN) || 0])
      })

      return {
        timeData,
        advncSpdData,
        rtnTqData,
        wtrPrsHData,
        rtnSpdData,
        frcstKnData
      }
    }

    // 创建图表配置
    const createChartOption = (processedData) => {
      const series = curveConfigs.map((config, index) => {
        const dataKey = config.field + 'Data'
        return {
          name: config.name,
          type: 'line',
          data: processedData[dataKey] || [],
          smooth: true,
          symbol: 'none',
          lineStyle: {
            color: config.color,
            width: 2
          },
          itemStyle: {
            color: config.color
          },
          yAxisIndex: index, // 每个曲线使用独立的Y轴
          connectNulls: false,
          sampling: 'lttb'
        }
      })

      // 创建多个Y轴
      const yAxis = curveConfigs.map((config, index) => ({
        type: 'value',
        name: `${config.name} (${config.unit})`,
        nameTextStyle: {
          color: config.color,
          fontSize: 12
        },
        position: index % 2 === 0 ? 'left' : 'right',
        offset: Math.floor(index / 2) * 60,
        axisLine: {
          show: true,
          lineStyle: {
            color: config.color
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: config.color
          }
        },
        axisLabel: {
          color: config.color,
          fontSize: 10
        },
        splitLine: {
          show: index === 0,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed'
          }
        }
      }))

      return {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          formatter: function(params) {
            if (!params || params.length === 0) return ''
            
            const time = new Date(params[0].axisValue).toLocaleString()
            let content = `<div style="margin-bottom: 5px; font-weight: bold;">${time}</div>`
            
            params.forEach(param => {
              if (param.value && param.value[1] !== undefined) {
                const config = curveConfigs[param.seriesIndex]
                content += `<div style="color: ${config.color};">
                  ${param.seriesName}: ${param.value[1].toFixed(2)} ${config.unit}
                </div>`
              }
            })
            
            return content
          }
        },
        legend: {
          data: curveConfigs.map(config => config.name),
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          top: 10,
          itemWidth: 20,
          itemHeight: 12
        },
        grid: {
          left: 80,
          right: 80,
          top: 50,
          bottom: 60,
          containLabel: false
        },
        xAxis: {
          type: 'time',
          name: '采集时间',
          nameTextStyle: {
            color: '#fff',
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.5)'
            }
          },
          axisTick: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.5)'
            }
          },
          axisLabel: {
            color: '#fff',
            fontSize: 10,
            formatter: function(value) {
              const date = new Date(value)
              return date.toLocaleTimeString()
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
              type: 'dashed'
            }
          }
        },
        yAxis: yAxis,
        series: series,
        animation: true,
        animationDuration: 300,
        animationEasing: 'cubicOut'
      }
    }

    // 更新图表
    const updateChart = () => {
      if (!chartInstance || !props.drillingData) return

      try {
        const processedData = processData(props.drillingData)
        const option = createChartOption(processedData)
        
        chartInstance.setOption(option, true)
      } catch (error) {
        console.error('更新简单钻进曲线图表失败:', error)
      }
    }

    // 初始化图表
    const initChart = () => {
      if (!simpleDrillCurveChart.value) return

      try {
        chartInstance = echarts.init(simpleDrillCurveChart.value)
        updateChart()

        // 监听窗口大小变化
        const handleResize = debouncedResize(() => {
          if (chartInstance) {
            chartInstance.resize()
          }
        }, 300)

        window.addEventListener('resize', handleResize)

        // 保存resize处理函数以便清理
        chartInstance._resizeHandler = handleResize
      } catch (error) {
        console.error('初始化简单钻进曲线图表失败:', error)
      }
    }

    // 监听数据变化
    watch(
      () => props.drillingData,
      () => {
        debouncedChartUpdate(updateChart, 100)
      },
      { deep: true }
    )

    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })

    onUnmounted(() => {
      if (chartInstance) {
        if (chartInstance._resizeHandler) {
          window.removeEventListener('resize', chartInstance._resizeHandler)
        }
        chartInstance.dispose()
        chartInstance = null
      }
      
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }
    })

    return {
      simpleDrillCurveChart
    }
  }
}
</script>

<style scoped>
.simple-drill-curve-chart-card {
  height: 350px;
  background: rgba(28, 28, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.simple-drill-curve-chart-card :deep(.el-card__header) {
  background: rgba(40, 40, 42, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
}

.sub-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

.simple-drill-curve-chart {
  width: 100%;
  height: 280px;
}

.simple-drill-curve-chart-card :deep(.el-card__body) {
  padding: 10px;
  height: calc(100% - 60px);
}
</style>

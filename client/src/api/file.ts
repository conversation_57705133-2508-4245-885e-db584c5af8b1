import request from '@/utils/request'
import type { FileListResponse, FileDetailResponse, StatsResponse } from '@/types/api'
import type { AxiosRequestHeaders } from 'axios'

/**
 * 获取文件和设备统计数据
 */
const getStats = (): Promise<StatsResponse> => {
  return request({
    url: '/files/stats',
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取文件列表
 */
const getFileList = (params: any): Promise<FileListResponse> => {
  return request({
    url: '/files',
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取设备文件列表
 */
const getDeviceFiles = (
  deviceId: string
): Promise<{
  success: boolean
  data: Array<{
    value: string
    label: string
    fileSize: number
    filePath: string
    createdAt: Date
  }>
}> => {
  return request({
    url: `/files/device/${deviceId}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取文件详情
 */
const getFileDetail = (id: string | number): Promise<FileDetailResponse> => {
  return request({
    url: `/files/${id}`,
    method: 'get',
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 上传文件
 */
const uploadFile = (data: any): Promise<any> => {
  return request({
    url: '/files',
    method: 'post',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 更新文件信息
 */
const updateFile = (id: string | number, data: any): Promise<any> => {
  return request({
    url: `/files/${id}`,
    method: 'put',
    data,
    headers: {} as AxiosRequestHeaders
  })
}

/**
 * 获取文件原始数据
 */
const getFileOriginalData = (fileId: string | number): Promise<any> => {
  if (!fileId) {
    console.error('getFileOriginalData: 缺少文件ID参数')
    return Promise.reject({ success: false, message: '缺少文件ID参数' })
  }
  
  console.log(`准备请求文件原始数据，fileId: ${fileId}`)
  return request({
    url: `/files/${fileId}/original-data`,
    method: 'get',
    headers: {} as AxiosRequestHeaders,
    timeout: 60000 // 设置更长的超时时间: 60秒
  }).catch(error => {
    console.error(`获取文件原始数据失败，fileId: ${fileId}`, error)
    throw error
  })
}

/**
 * 根据条件查询原始数据
 * @param params 查询参数，包含deviceId（必选）、startTime、endTime、holeNo（可选）
 */
const getRawData = (params: {
  deviceId: string;
  startTime?: string;
  endTime?: string;
  holeNo?: string;
}): Promise<any> => {
  return request({
    url: '/data/raw-query',
    method: 'get',
    params,
    headers: {} as AxiosRequestHeaders,
    timeout: 60000 // 设置更长的超时时间: 60秒
  })
}

/**
 * 删除文件
 */
const deleteFile = (id: string | number): Promise<any> => {
  return request({
    url: `/files/${id}`,
    method: 'delete',
    headers: {} as AxiosRequestHeaders
  })
}

export const fileApi = {
  getStats,
  getFileList,
  getDeviceFiles,
  getFileDetail,
  uploadFile,
  updateFile,
  deleteFile,
  getFileOriginalData,
  getRawData
}

import { Router } from 'express'
import {
  getAllTemplates,
  getTemplateById,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  setTemplateStatus,
  getTemplateStats,
  copyTemplate
} from '../controllers/template'
import { asyncHandler } from '../utils/asyncHandler'
import { verifyToken } from '../middleware/auth'

const router = Router()

// 获取模板列表
router.get('/', asyncHandler(getAllTemplates))

// 获取模板统计信息
router.get('/stats', asyncHandler(getTemplateStats))

// 获取模板详情
router.get('/:id', asyncHandler(getTemplateById))

// 创建模板
router.post('/', asyncHandler(verifyToken), asyncHandler(createTemplate))

// 更新模板
router.put('/:id', asyncHandler(verifyToken), asyncHandler(updateTemplate))

// 删除模板
router.delete('/:id', asyncHandler(verifyToken), asyncHandler(deleteTemplate))

// 启用/禁用模板
router.patch('/:id/status', asyncHandler(verifyToken), asyncHandler(setTemplateStatus))

// 复制模板
router.post('/:id/copy', asyncHandler(verifyToken), asyncHandler(copyTemplate))

export default router
<template>
  <div class="overview-container">
    <div class="stat-cards">
      <el-row :gutter="24">
        <el-col
          v-for="(item, index) in statCards"
          :key="index"
          :xs="24"
          :sm="12"
          :md="6"
        >
          <el-card
            class="stat-card"
            :class="`stat-card-${index}`"
            shadow="hover"
          >
            <div class="stat-value">
              {{ item.value }}
            </div>
            <div class="stat-label">
              {{ item.label }}
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和列表整合在一个卡片中 -->
    <el-card class="data-list-card">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-row :gutter="24">
          <el-col :span="4">
            <el-input
              v-model="filterForm.deviceSn"
              placeholder="设备序列号"
              clearable
            />
          </el-col>
          <el-col :span="4">
            <el-input
              v-model="filterForm.fileName"
              placeholder="文件名称"
              clearable
            />
          </el-col>
          <el-col :span="8">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </el-col>
          <el-col
            :span="8"
            class="filter-buttons"
          >
            <el-button
              type="primary"
              @click="searchFiles"
            >
              搜索
            </el-button>
            <el-button @click="resetForm">
              清空
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="fileList"
        style="width: 100%"
        border
      >
        <el-table-column
          align="center"
          prop="device_sn"
          label="设备序列号"
        />
        <el-table-column
          align="center"
          prop="file_name"
          label="文件名称"
        />
        <el-table-column
          label="文件上传时间"
          align="center"
        >
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
          label="文件大小"
          align="center"
        >
          <template #default="scope">
            {{ formatFileSize(scope.row.file_size) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页部分 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, prev, pager, next"
          :total="total"
          :current-page="currentPage"
          :page-size="pageSize"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { fileApi } from '@/api'
import { formatDateTime, formatFileSize } from '@/utils/utils'
import { debouncedApiCall, debouncedSearch } from '@/utils/debounce'

// 统计卡片数据
const statCards = reactive([
  { label: '设备总量', value: '0' },
  { label: '数据量', value: '0' },
  { label: '清洗完成量', value: '0' },
  { label: '使用空间', value: '0' }
])

// 筛选条件
const filterForm = reactive({
  deviceSn: '',
  fileName: '',
  startDate: '',
  endDate: ''
})
const dateRange = ref([])
const loading = ref(false)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 文件列表数据
const fileList = ref([])

// 获取文件列表
const fetchFileList = async () => {
  try {
    loading.value = true
    const params = {
      deviceSn: filterForm.deviceSn,
      fileName: filterForm.fileName,
      startDate: filterForm.startDate,
      endDate: filterForm.endDate,
      page: currentPage.value,
      pageSize: pageSize.value
    }

    const res = await fileApi.getFileList(params)
    if (res.success) {
      fileList.value = res.data.list
      total.value = res.data.total
    } else {
      ElMessage.error('获取文件列表失败')
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    ElMessage.error('获取文件列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const res = await fileApi.getStats()
    if (res.success) {
      statCards[0].value = res.data.deviceCount.toString()
      statCards[1].value = res.data.dataRows.toString()
      statCards[2].value = res.data.cleanedCount.toString()
      statCards[3].value = res.data.formattedSize
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 日期范围变化处理
const handleDateChange = val => {
  if (val) {
    filterForm.startDate = val[0]
    filterForm.endDate = val[1]
  } else {
    filterForm.startDate = ''
    filterForm.endDate = ''
  }
}

// 创建防抖的搜索函数
const debouncedFetchFileList = debouncedApiCall(() => {
  currentPage.value = 1
  fetchFileList()
})

// 创建防抖的输入搜索函数
const debouncedInputSearch = debouncedSearch(() => {
  currentPage.value = 1
  fetchFileList()
})

// 搜索文件
const searchFiles = () => {
  debouncedFetchFileList()
}

// 重置表单
const resetForm = () => {
  filterForm.deviceSn = ''
  filterForm.fileName = ''
  filterForm.startDate = ''
  filterForm.endDate = ''
  dateRange.value = []
  currentPage.value = 1
  fetchFileList()
}

// 页面切换
const handlePageChange = page => {
  currentPage.value = page
  fetchFileList()
}

// 监听输入框变化，实现实时搜索
watch(
  () => [filterForm.deviceSn, filterForm.fileName],
  () => {
    debouncedInputSearch()
  },
  { deep: true }
)

onMounted(() => {
  // 初始加载文件列表
  fetchFileList()
  // 获取统计数据
  fetchStats()
})

// 组件卸载时清理防抖函数
onUnmounted(() => {
  if (debouncedFetchFileList && typeof debouncedFetchFileList.cancel === 'function') {
    debouncedFetchFileList.cancel()
  }
  if (debouncedInputSearch && typeof debouncedInputSearch.cancel === 'function') {
    debouncedInputSearch.cancel()
  }
})
</script>

<style scoped>
.overview-container {
  padding: 24px;
  background-color: var(--apple-background);
}

.stat-cards {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: var(--apple-text-primary);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: var(--apple-text-secondary);
}

/* 整合搜索和列表的卡片 */
.data-list-card {
  margin-bottom: 24px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-buttons {
  text-align: right;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .filter-buttons {
    text-align: left;
    margin-top: 16px;
  }
}
</style>

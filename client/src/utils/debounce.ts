/**
 * 统一的防抖和节流工具库
 * 提供标准化的防抖和节流函数，确保项目中的性能优化一致性
 */

import { debounce, throttle } from 'lodash-es'

// 统一的防抖配置常量
export const DEBOUNCE_DELAYS = {
  SEARCH: 300,        // 搜索输入防抖延迟
  RESIZE: 150,        // 窗口大小变化防抖延迟
  DRAG: 16,           // 拖拽操作节流延迟（60fps）
  API_CALL: 300,      // API调用防抖延迟
  CHART_UPDATE: 100,  // 图表更新防抖延迟
  MQTT_THROTTLE: 1000, // MQTT消息节流延迟
  CONFIG_UPDATE: 200,  // 配置更新防抖延迟
  INPUT_FILTER: 300    // 输入过滤防抖延迟
} as const

/**
 * 创建防抖函数的工厂方法
 * @param fn 需要防抖的函数
 * @param delay 防抖延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export const createDebounced = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number = DEBOUNCE_DELAYS.API_CALL
) => {
  return debounce(fn, delay)
}

/**
 * 创建节流函数的工厂方法
 * @param fn 需要节流的函数
 * @param delay 节流延迟时间（毫秒）
 * @returns 节流后的函数
 */
export const createThrottled = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number = DEBOUNCE_DELAYS.DRAG
) => {
  return throttle(fn, delay)
}

// 预定义的防抖函数工厂
/**
 * 创建API调用防抖函数
 * 适用于搜索、筛选等API请求场景
 */
export const debouncedApiCall = <T extends (...args: any[]) => any>(fn: T) => 
  createDebounced(fn, DEBOUNCE_DELAYS.API_CALL)

/**
 * 创建搜索输入防抖函数
 * 适用于搜索框输入场景
 */
export const debouncedSearch = <T extends (...args: any[]) => any>(fn: T) => 
  createDebounced(fn, DEBOUNCE_DELAYS.SEARCH)

/**
 * 创建输入过滤防抖函数
 * 适用于实时过滤场景
 */
export const debouncedFilter = <T extends (...args: any[]) => any>(fn: T) => 
  createDebounced(fn, DEBOUNCE_DELAYS.INPUT_FILTER)

/**
 * 创建图表更新防抖函数
 * 适用于图表数据更新场景
 */
export const debouncedChartUpdate = <T extends (...args: any[]) => any>(fn: T) => 
  createDebounced(fn, DEBOUNCE_DELAYS.CHART_UPDATE)

/**
 * 创建配置更新防抖函数
 * 适用于配置保存场景
 */
export const debouncedConfigUpdate = <T extends (...args: any[]) => any>(fn: T) => 
  createDebounced(fn, DEBOUNCE_DELAYS.CONFIG_UPDATE)

/**
 * 创建窗口大小变化防抖函数
 * 适用于resize事件处理
 */
export const debouncedResize = <T extends (...args: any[]) => any>(fn: T) => 
  createDebounced(fn, DEBOUNCE_DELAYS.RESIZE)

// 预定义的节流函数工厂
/**
 * 创建拖拽操作节流函数
 * 适用于拖拽、滚动等高频操作
 */
export const throttledDrag = <T extends (...args: any[]) => any>(fn: T) => 
  createThrottled(fn, DEBOUNCE_DELAYS.DRAG)

/**
 * 创建MQTT消息节流函数
 * 适用于高频消息处理场景
 */
export const throttledMqtt = <T extends (...args: any[]) => any>(fn: T) => 
  createThrottled(fn, DEBOUNCE_DELAYS.MQTT_THROTTLE)

/**
 * 取消防抖函数的执行
 * @param debouncedFn 防抖函数
 */
export const cancelDebounced = (debouncedFn: any) => {
  if (debouncedFn && typeof debouncedFn.cancel === 'function') {
    debouncedFn.cancel()
  }
}

/**
 * 立即执行防抖函数
 * @param debouncedFn 防抖函数
 */
export const flushDebounced = (debouncedFn: any) => {
  if (debouncedFn && typeof debouncedFn.flush === 'function') {
    debouncedFn.flush()
  }
}

/**
 * Vue 3 Composition API 专用的防抖 hook
 * @param fn 需要防抖的函数
 * @param delay 防抖延迟
 * @returns 防抖函数和控制方法
 */
export const useDebounce = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number = DEBOUNCE_DELAYS.API_CALL
) => {
  const debouncedFn = createDebounced(fn, delay)
  
  return {
    debouncedFn,
    cancel: () => cancelDebounced(debouncedFn),
    flush: () => flushDebounced(debouncedFn)
  }
}

/**
 * Vue 3 Composition API 专用的节流 hook
 * @param fn 需要节流的函数
 * @param delay 节流延迟
 * @returns 节流函数和控制方法
 */
export const useThrottle = <T extends (...args: any[]) => any>(
  fn: T,
  delay: number = DEBOUNCE_DELAYS.DRAG
) => {
  const throttledFn = createThrottled(fn, delay)

  return {
    throttledFn,
    cancel: () => {
      if (throttledFn && typeof (throttledFn as any).cancel === 'function') {
        (throttledFn as any).cancel()
      }
    },
    flush: () => {
      if (throttledFn && typeof (throttledFn as any).flush === 'function') {
        (throttledFn as any).flush()
      }
    }
  }
}

/**
 * 批量清理防抖/节流函数的工具函数
 * @param functions 需要清理的防抖/节流函数数组
 */
export const cleanupDebouncedFunctions = (functions: any[]) => {
  functions.forEach(fn => {
    if (fn && typeof fn.cancel === 'function') {
      fn.cancel()
    }
  })
}

/**
 * Vue 3 组合式API的防抖清理hook
 * 自动在组件卸载时清理所有注册的防抖/节流函数
 */
export const useDebounceCleanup = () => {
  const debouncedFunctions: any[] = []

  const registerDebounced = (fn: any) => {
    debouncedFunctions.push(fn)
    return fn
  }

  const cleanup = () => {
    cleanupDebouncedFunctions(debouncedFunctions)
    debouncedFunctions.length = 0
  }

  // 在Vue 3环境中自动清理
  if (typeof window !== 'undefined' && window.Vue) {
    const { onUnmounted } = window.Vue
    if (onUnmounted) {
      onUnmounted(cleanup)
    }
  }

  return {
    registerDebounced,
    cleanup
  }
}

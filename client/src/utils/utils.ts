/**
 * 通用工具函数集
 */

/**
 * 获取设备类型对应的标签样式
 * @param type 设备类型代码
 * @returns Element Plus标签类型
 */
export const getDeviceTagType = (type: string): string => {
  switch (type) {
    case 'AD':
      return 'primary' // 蓝色
    case 'WPD':
      return 'warning' // 黄色
    case 'PDF':
      return 'success' // 绿色
    default:
      return 'info' // 灰色
  }
}

/**
 * 格式化日期时间
 * @param date 日期对象或日期字符串或时间戳
 * @param defaultValue 当日期无效时返回的默认值
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (
  date: Date | string | number | undefined | null,
  defaultValue = '未知'
): string => {
  if (!date) return defaultValue

  try {
    const dateObj = date instanceof Date ? date : new Date(date)
    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) return defaultValue
    return dateObj.toLocaleString()
  } catch (error) {
    return defaultValue
  }
}

/**
 * 格式化文件大小
 * @param sizeInBytes 文件大小（字节）
 * @param defaultValue 当大小无效时返回的默认值
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (
  sizeInBytes: number | undefined | null,
  defaultValue = '未知'
): string => {
  if (sizeInBytes === undefined || sizeInBytes === null) return defaultValue

  if (sizeInBytes < 1024) {
    return sizeInBytes + ' B'
  } else if (sizeInBytes < 1024 * 1024) {
    return (sizeInBytes / 1024).toFixed(2) + ' KB'
  } else if (sizeInBytes < 1024 * 1024 * 1024) {
    return (sizeInBytes / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (sizeInBytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

/**
 * 格式化工作模式
 * @param mode 钻机工作模式: 0：待机状态、1：W80水锤钻模式、2：W80跟管钻进模式、3：PDC纯旋转钻孔模式&钻注一体、4：封孔注浆模式、5：定点取芯模式、6：绳索取芯钻进模式
 * @returns 格式化后的工作模式字符串
 */
export const formatWorkMode = (mode: number): string => {
  switch (mode) {
    case 0:
      return '待机状态'
    case 1:
      return 'W80水锤钻模式'
    case 2:
      return 'W80跟管钻进模式'
    case 3:
      return 'PDC纯旋转钻孔模式&钻注一体'
    case 4:
      return '封孔注浆模式'
    case 5:
      return '定点取芯模式'
    case 6:
      return '绳索取芯钻进模式'
  } 
  return ""
}
